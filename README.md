# BlendPro - Advanced AI-Powered Blender Assistant

🚀 **NEW**: MCP (Model Context Protocol) Integration! Connect to external tools and resources for enhanced AI-driven Blender operations.

![Whisk_18340afb71](https://github.com/user-attachments/assets/f9d1489e-07f8-4e4d-b2de-546bee7002e3)



**Author:** [inkbytefo](https://github.com/inkbytefo)

BlendPro is a powerful Blender addon that integrates AI language models directly into your 3D workflow. Generate complex Blender Python scripts using natural language commands, with advanced features like code preview, auto-save, export/import, undo functionality, and now MCP integration for external resource access.

## ✨ Key Features

- 🤖 **AI-Powered Code Generation**: Generate Blender Python scripts using natural language
- 🌐 **MCP Integration**: Connect to external tools and resources via Model Context Protocol
- ⚙️ **Advanced AI Configuration**: Fine-tune temperature, max tokens, and top-p parameters
- 💾 **Auto-Save Chat History**: Automatically saves and restores your conversation history
- 📁 **Export/Import Sessions**: Backup and share your chat sessions as JSON files
- 👁️ **Code Preview**: Review generated code before execution for safety
- ↩️ **Undo/Redo System**: Automatic scene backups with one-click undo functionality
- 🔧 **Multi-Model Support**: Works with OpenAI, Anthropic, and local models
- 🎯 **User-Friendly Interface**: Clean, intuitive UI integrated into Blender's sidebar

## 🚀 What's New in BlendPro

This enhanced version includes major improvements over the original BlenderGPT:
- **Smart Code Preview**: See exactly what code will run before execution
- **Persistent Chat History**: Never lose your conversations again
- **Advanced AI Controls**: Professional-grade AI parameter tuning
- **Backup & Recovery**: Automatic scene state management with undo capability
- **Session Management**: Export and import your chat sessions
- **🆕 Enhanced MCP Integration**:
  - Claude Desktop compatible JSON configuration
  - Multiple MCP server support with advanced security
  - Auto-start capability and improved UI
  - Backward compatibility with existing setups

## 📦 Installation

1. Download the latest release from [GitHub](https://github.com/inkbytefo/BlendPro)
2. Open Blender, go to `Edit > Preferences > Add-ons > Install`
3. Select the downloaded ZIP file and click `Install Add-on`
4. Enable the add-on by checking the checkbox next to `GPT-4 Blender Assistant`
5. Configure your API settings in the addon preferences

## ⚙️ Configuration

### API Setup
1. Go to `Edit > Preferences > Add-ons > GPT-4 Blender Assistant`
2. Enter your **API Key** (OpenAI, Anthropic, or compatible service)
3. Set **Base URL** if using custom endpoints (optional)
4. Configure **AI Parameters**:
   - **Temperature** (0.0-2.0): Controls creativity vs consistency
   - **Max Tokens** (1-4000): Maximum response length
   - **Top P** (0.0-1.0): Controls response diversity

### Supported Models
- **OpenAI**: GPT-4, GPT-4 Turbo, GPT-3.5 Turbo, GPT-4o, GPT-4o Mini
- **Custom Models**: Any OpenAI-compatible API
- **Local Models**: Self-hosted AI models

## 🎮 Usage

1. Open the **3D View** sidebar (press `N` if not visible)
2. Navigate to the **GPT-4 Assistant** tab
3. Type your command: *"Create a spiral staircase with 20 steps"*
4. Click **Execute** to generate code
5. Review the code in the **Preview Dialog**
6. Click **Execute Code** to run it in Blender

### Example Commands
- "Create 10 random cubes between -5 and 5 on all axes"
- "Add a material with red color to the selected object"
- "Create a camera looking at the origin from 10 units away"
- "Generate a procedural landscape with noise texture"

## 🛠️ Advanced Features

### Chat Management
- **Auto-Save**: Conversations automatically saved and restored
- **Export Chat**: Save sessions as JSON files for backup
- **Import Chat**: Load previous conversations
- **Clear Chat**: Start fresh while keeping backups

### Safety & Recovery
- **Code Preview**: Always review before execution
- **Auto Backup**: Scene state saved before each operation
- **Undo Last**: One-click recovery from mistakes
- **Backup Management**: View and cleanup old backups

## 🔧 Requirements

- **Blender**: 3.1 or later
- **API Key**: OpenAI, Anthropic, or compatible service
- **Internet**: For API calls (unless using local models)

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Original BlenderGPT concept and foundation
- OpenAI for providing powerful language models
- Blender Foundation for the amazing 3D software
- The open-source community for inspiration and support

---

**Developed by [inkbytefo](https://github.com/inkbytefo)** | **Repository**: [BlendPro](https://github.com/inkbytefo/BlendPro)
