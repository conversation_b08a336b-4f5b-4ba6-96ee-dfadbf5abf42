"""
MCP Server Manager for BlendPro
Coordinates multiple MCP servers and provides unified interface.

Author: inkbytefo
"""

import os
import sys
import time
import threading
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# Add lib directory to path for bundled dependencies
lib_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), "lib")
if lib_path not in sys.path:
    sys.path.append(lib_path)

from mcp_config_parser import MCPConfiguration, MCPServerConfig, LegacyMCPConfig, MCPConfigParser
from mcp_process_manager import MCPProcessManager, ProcessInfo, ProcessStatus
from mcp_client import MCPClient, MCPError
from mcp_security import MCPSecurityValidator, MCPSecurityError


@dataclass
class ServerCapabilities:
    """Aggregated server capabilities"""
    tools: List[Dict[str, Any]] = field(default_factory=list)
    resources: List[Dict[str, Any]] = field(default_factory=list)
    prompts: List[Dict[str, Any]] = field(default_factory=list)
    server_name: str = ""
    
    def merge(self, other: 'ServerCapabilities') -> 'ServerCapabilities':
        """Merge capabilities from another server"""
        merged = ServerCapabilities(
            tools=self.tools + other.tools,
            resources=self.resources + other.resources,
            prompts=self.prompts + other.prompts,
            server_name=f"{self.server_name},{other.server_name}" if self.server_name else other.server_name
        )
        return merged


@dataclass
class ServerConnection:
    """Information about a server connection"""
    name: str
    config: Union[MCPServerConfig, LegacyMCPConfig]
    client: Optional[MCPClient] = None
    process_info: Optional[ProcessInfo] = None
    capabilities: Optional[ServerCapabilities] = None
    last_error: Optional[str] = None
    connection_time: Optional[float] = None
    is_legacy: bool = False
    
    @property
    def is_connected(self) -> bool:
        """Check if server is connected"""
        if self.is_legacy:
            return self.client and self.client.is_connected()
        else:
            return (self.process_info and 
                   self.process_info.is_alive and 
                   self.client and 
                   self.client.is_connected())
    
    @property
    def status_summary(self) -> str:
        """Get human-readable status summary"""
        if self.is_legacy:
            return "Connected" if self.is_connected else "Disconnected"
        else:
            if not self.process_info:
                return "Not Started"
            return self.process_info.status.value.title()


class MCPServerManager:
    """Unified manager for multiple MCP servers"""
    
    def __init__(self, log_callback: Optional[Callable[[str, str], None]] = None,
                 strict_security: bool = True):
        """
        Initialize server manager

        Args:
            log_callback: Optional callback for logging (level, message)
            strict_security: Enable strict security validation
        """
        self.log_callback = log_callback
        self.process_manager = MCPProcessManager(log_callback)
        self.connections: Dict[str, ServerConnection] = {}
        self.config: Optional[MCPConfiguration] = None
        self._lock = threading.Lock()
        self._capability_cache_time = 0
        self._capability_cache: Optional[ServerCapabilities] = None
        self.CACHE_DURATION = 300  # 5 minutes
        self.security_validator = MCPSecurityValidator(strict_mode=strict_security)
    
    def _log(self, level: str, message: str) -> None:
        """Log message using callback or print"""
        if self.log_callback:
            self.log_callback(level, message)
        else:
            print(f"MCP Server Manager [{level}]: {message}")
    
    def load_configuration(self, config_path: str) -> bool:
        """
        Load configuration from file with security validation

        Args:
            config_path: Path to configuration file

        Returns:
            bool: True if loaded successfully
        """
        try:
            parser = MCPConfigParser()
            self.config = parser.load_from_file(config_path)

            # Security validation
            is_valid, warnings, errors = self.security_validator.validate_configuration(self.config)

            # Log warnings
            for warning in warnings:
                self._log("WARNING", f"Security: {warning}")

            # Log errors and fail if any
            for error in errors:
                self._log("ERROR", f"Security: {error}")

            if not is_valid:
                self._log("ERROR", "Configuration failed security validation")
                return False

            # Basic validation
            basic_warnings = parser.validate_config(self.config)
            for warning in basic_warnings:
                self._log("WARNING", warning)

            self._log("INFO", f"Configuration loaded and validated: {len(self.config.servers)} servers")
            return True

        except Exception as e:
            self._log("ERROR", f"Failed to load configuration: {e}")
            return False
    
    def set_configuration(self, config: MCPConfiguration) -> bool:
        """
        Set configuration directly with security validation

        Args:
            config: Configuration to set

        Returns:
            bool: True if configuration is valid and set successfully
        """
        try:
            # Security validation
            is_valid, warnings, errors = self.security_validator.validate_configuration(config)

            # Log warnings
            for warning in warnings:
                self._log("WARNING", f"Security: {warning}")

            # Log errors and fail if any
            for error in errors:
                self._log("ERROR", f"Security: {error}")

            if not is_valid:
                self._log("ERROR", "Configuration failed security validation")
                return False

            self.config = config
            self._log("INFO", f"Configuration set and validated: {len(config.servers)} servers")
            return True

        except Exception as e:
            self._log("ERROR", f"Failed to set configuration: {e}")
            return False
    
    def start_all_servers(self) -> Dict[str, bool]:
        """
        Start all enabled servers
        
        Returns:
            Dict[str, bool]: Results for each server
        """
        if not self.config:
            self._log("ERROR", "No configuration loaded")
            return {}
        
        results = {}
        
        # Start legacy server if enabled
        if self.config.legacy.enabled:
            results["legacy"] = self._start_legacy_server()
        
        # Start configured servers
        enabled_servers = self.config.get_enabled_servers()
        for name, server_config in enabled_servers.items():
            results[name] = self.start_server(name)
        
        return results
    
    def start_server(self, name: str) -> bool:
        """
        Start a specific server
        
        Args:
            name: Server name
            
        Returns:
            bool: True if started successfully
        """
        if not self.config:
            self._log("ERROR", "No configuration loaded")
            return False
        
        if name == "legacy":
            return self._start_legacy_server()
        
        if name not in self.config.servers:
            self._log("ERROR", f"Server '{name}' not found in configuration")
            return False
        
        server_config = self.config.servers[name]

        try:
            # Additional security validation before starting
            try:
                self.security_validator.validate_server_config(server_config)
            except MCPSecurityError as e:
                self._log("ERROR", f"Security validation failed for server '{name}': {e}")
                return False

            with self._lock:
                # Start process
                if not self.process_manager.start_server(server_config):
                    return False
                
                # Get process info
                process_info = self.process_manager.get_server_status(name)
                if not process_info:
                    self._log("ERROR", f"Failed to get process info for '{name}'")
                    return False
                
                # Create MCP client for stdio communication
                client = MCPClient(
                    server_url="stdio",  # Special marker for stdio
                    transport_type="stdio",
                    timeout=server_config.timeout
                )
                
                # Connect client to process
                client.process = process_info.process
                client.connected = True
                
                # Create connection info
                connection = ServerConnection(
                    name=name,
                    config=server_config,
                    client=client,
                    process_info=process_info,
                    connection_time=time.time(),
                    is_legacy=False
                )
                
                self.connections[name] = connection
                
                # Discover capabilities in background
                threading.Thread(
                    target=self._discover_capabilities_async,
                    args=(name,),
                    daemon=True
                ).start()
                
                self._log("INFO", f"Server '{name}' started successfully")
                return True
                
        except Exception as e:
            self._log("ERROR", f"Failed to start server '{name}': {e}")
            return False
    
    def stop_server(self, name: str) -> bool:
        """
        Stop a specific server
        
        Args:
            name: Server name
            
        Returns:
            bool: True if stopped successfully
        """
        with self._lock:
            if name == "legacy":
                return self._stop_legacy_server()
            
            if name not in self.connections:
                self._log("WARNING", f"Server '{name}' not found in connections")
                return True
            
            connection = self.connections[name]
            
            try:
                # Disconnect client
                if connection.client:
                    connection.client.disconnect()
                
                # Stop process
                success = self.process_manager.stop_server(name)
                
                # Remove connection
                del self.connections[name]
                
                self._log("INFO", f"Server '{name}' stopped")
                return success
                
            except Exception as e:
                self._log("ERROR", f"Failed to stop server '{name}': {e}")
                return False
    
    def restart_server(self, name: str) -> bool:
        """Restart a specific server"""
        self._log("INFO", f"Restarting server '{name}'")
        
        if not self.stop_server(name):
            return False
        
        time.sleep(1)  # Brief pause
        
        return self.start_server(name)
    
    def stop_all_servers(self) -> None:
        """Stop all running servers"""
        self._log("INFO", "Stopping all servers")
        
        server_names = list(self.connections.keys())
        for name in server_names:
            self.stop_server(name)
    
    def get_server_status(self, name: str) -> Optional[ServerConnection]:
        """Get status of a specific server"""
        return self.connections.get(name)
    
    def list_servers(self) -> Dict[str, ServerConnection]:
        """Get all server connections"""
        return self.connections.copy()
    
    def get_aggregated_capabilities(self, force_refresh: bool = False) -> ServerCapabilities:
        """
        Get aggregated capabilities from all connected servers
        
        Args:
            force_refresh: Force refresh of capabilities
            
        Returns:
            ServerCapabilities: Aggregated capabilities
        """
        current_time = time.time()
        
        # Use cache if available and not expired
        if (not force_refresh and 
            self._capability_cache and 
            current_time - self._capability_cache_time < self.CACHE_DURATION):
            return self._capability_cache
        
        # Aggregate capabilities from all connected servers
        aggregated = ServerCapabilities()
        
        for name, connection in self.connections.items():
            if connection.is_connected and connection.capabilities:
                aggregated = aggregated.merge(connection.capabilities)
        
        # Update cache
        self._capability_cache = aggregated
        self._capability_cache_time = current_time
        
        return aggregated
    
    def execute_tool(self, tool_name: str, params: Dict[str, Any], 
                    server_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Execute a tool on a specific server or find the best server
        
        Args:
            tool_name: Name of tool to execute
            params: Tool parameters
            server_name: Optional specific server name
            
        Returns:
            Dict: Tool execution result
        """
        if server_name:
            # Execute on specific server
            if server_name not in self.connections:
                raise MCPError(f"Server '{server_name}' not found")
            
            connection = self.connections[server_name]
            if not connection.is_connected:
                raise MCPError(f"Server '{server_name}' not connected")
            
            return connection.client.execute_tool(tool_name, params)
        
        else:
            # Find server that has this tool
            for name, connection in self.connections.items():
                if (connection.is_connected and 
                    connection.capabilities and 
                    any(tool.get("name") == tool_name for tool in connection.capabilities.tools)):
                    
                    return connection.client.execute_tool(tool_name, params)
            
            raise MCPError(f"Tool '{tool_name}' not found on any connected server")
    
    def fetch_resource(self, resource_uri: str, 
                      server_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Fetch a resource from a specific server or find the best server
        
        Args:
            resource_uri: URI of resource to fetch
            server_name: Optional specific server name
            
        Returns:
            Dict: Resource content
        """
        if server_name:
            # Fetch from specific server
            if server_name not in self.connections:
                raise MCPError(f"Server '{server_name}' not found")
            
            connection = self.connections[server_name]
            if not connection.is_connected:
                raise MCPError(f"Server '{server_name}' not connected")
            
            return connection.client.fetch_resource(resource_uri)
        
        else:
            # Find server that has this resource
            for name, connection in self.connections.items():
                if (connection.is_connected and 
                    connection.capabilities and 
                    any(resource.get("uri") == resource_uri for resource in connection.capabilities.resources)):
                    
                    return connection.client.fetch_resource(resource_uri)
            
            raise MCPError(f"Resource '{resource_uri}' not found on any connected server")
    
    def _start_legacy_server(self) -> bool:
        """Start legacy single-server configuration"""
        if not self.config or not self.config.legacy.enabled:
            return False
        
        try:
            legacy_config = self.config.legacy
            
            client = MCPClient(
                server_url=legacy_config.server_url,
                transport_type=legacy_config.transport_type,
                auth_token=legacy_config.auth_token,
                timeout=legacy_config.timeout
            )
            
            if not client.connect():
                return False
            
            connection = ServerConnection(
                name="legacy",
                config=legacy_config,
                client=client,
                connection_time=time.time(),
                is_legacy=True
            )
            
            self.connections["legacy"] = connection
            
            # Discover capabilities
            threading.Thread(
                target=self._discover_capabilities_async,
                args=("legacy",),
                daemon=True
            ).start()
            
            self._log("INFO", "Legacy server started successfully")
            return True
            
        except Exception as e:
            self._log("ERROR", f"Failed to start legacy server: {e}")
            return False
    
    def _stop_legacy_server(self) -> bool:
        """Stop legacy server"""
        if "legacy" not in self.connections:
            return True
        
        try:
            connection = self.connections["legacy"]
            if connection.client:
                connection.client.disconnect()
            
            del self.connections["legacy"]
            self._log("INFO", "Legacy server stopped")
            return True
            
        except Exception as e:
            self._log("ERROR", f"Failed to stop legacy server: {e}")
            return False
    
    def _discover_capabilities_async(self, server_name: str) -> None:
        """Discover server capabilities in background"""
        try:
            if server_name not in self.connections:
                return
            
            connection = self.connections[server_name]
            if not connection.client:
                return
            
            # Discover capabilities
            capabilities_data = connection.client.discover_capabilities()
            
            # Convert to ServerCapabilities
            capabilities = ServerCapabilities(
                tools=capabilities_data.get("tools", []),
                resources=capabilities_data.get("resources", []),
                prompts=capabilities_data.get("prompts", []),
                server_name=server_name
            )
            
            connection.capabilities = capabilities
            
            self._log("INFO", f"Discovered capabilities for '{server_name}': "
                            f"{len(capabilities.tools)} tools, "
                            f"{len(capabilities.resources)} resources, "
                            f"{len(capabilities.prompts)} prompts")
            
            # Clear capability cache to force refresh
            self._capability_cache = None
            
        except Exception as e:
            self._log("ERROR", f"Failed to discover capabilities for '{server_name}': {e}")
            if server_name in self.connections:
                self.connections[server_name].last_error = str(e)
    
    def cleanup(self) -> None:
        """Cleanup all resources"""
        self.stop_all_servers()
        self.process_manager.cleanup()
    
    def __del__(self):
        """Destructor to ensure cleanup"""
        try:
            self.cleanup()
        except:
            pass
