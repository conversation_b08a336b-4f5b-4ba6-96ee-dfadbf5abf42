{"config_version": "1.0", "mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:\\Users\\<USER>\\OneDrive\\Desktop"], "enabled": true, "timeout": 30, "transport_type": "stdio"}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "enabled": true, "timeout": 30, "transport_type": "stdio"}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "<YOUR_TOKEN>"}, "enabled": false, "timeout": 30, "transport_type": "stdio"}}, "legacy": {"enabled": false, "server_url": "", "transport_type": "http", "timeout": 30}}