import sys
import os
import bpy
import bpy.props
import re

# Add the 'libs' folder to the Python path
libs_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), "lib")
if libs_path not in sys.path:
    sys.path.append(libs_path)

# OpenAI client will be created when needed

from .utilities import *
bl_info = {
    "name": "BlendPro - Advanced AI Blender Assistant",
    "blender": (2, 82, 0),
    "category": "Object",
    "author": "inkbytefo",
    "version": (3, 0, 0),
    "location": "3D View > UI > GPT-4 Blender Assistant",
    "description": "Advanced AI-powered Blender assistant with code preview, auto-save, export/import, and undo features.",
    "warning": "",
    "wiki_url": "https://github.com/inkbytefo/BlendPro",
    "tracker_url": "https://github.com/inkbytefo/BlendPro/issues",
}

system_prompt = """You are an assistant made for the purposes of helping the user with Blender, the 3D software. 
- Respond with your answers in markdown (```). 
- Preferably import entire modules instead of bits. 
- Do not perform destructive operations on the meshes. 
- Do not use cap_ends. Do not do more than what is asked (setting up render settings, adding cameras, etc)
- Do not respond with anything that is not Python code.

Example:

user: create 10 cubes in random locations from -10 to 10
assistant:
```
import bpy
import random
bpy.ops.mesh.primitive_cube_add()

#how many cubes you want to add
count = 10

for c in range(0,count):
    x = random.randint(-10,10)
    y = random.randint(-10,10)
    z = random.randint(-10,10)
    bpy.ops.mesh.primitive_cube_add(location=(x,y,z))
```"""



class BLENDPRO_OT_DeleteMessage(bpy.types.Operator):
    bl_idname = "blendpro.delete_message"
    bl_label = "Delete Message"
    bl_options = {'REGISTER', 'UNDO'}

    message_index: bpy.props.IntProperty()

    def execute(self, context):
        context.scene.blendpro_chat_history.remove(self.message_index)
        return {'FINISHED'}

class BLENDPRO_OT_ConfirmMCPConnection(bpy.types.Operator):
    bl_idname = "blendpro.confirm_mcp_connection"
    bl_label = "Confirm MCP Connection"
    bl_description = "Confirm connection to external MCP server for security"
    bl_options = {'REGISTER'}

    server_url: bpy.props.StringProperty(
        name="Server URL",
        description="MCP server URL to connect to",
        default=""
    )

    def execute(self, context):
        # User confirmed, proceed with MCP operations
        self.report({'INFO'}, f"MCP connection to {self.server_url} confirmed")
        return {'FINISHED'}

    def invoke(self, context, event):
        # Show confirmation dialog
        return context.window_manager.invoke_confirm(self, event)

    def draw(self, context):
        layout = self.layout
        layout.label(text="Security Warning:", icon="ERROR")
        layout.separator()
        layout.label(text="You are about to connect to an external MCP server:")
        layout.label(text=f"URL: {self.server_url}")
        layout.separator()
        layout.label(text="This will allow the server to:")
        layout.label(text="• Access your prompts and context")
        layout.label(text="• Execute tools and fetch resources")
        layout.label(text="• Potentially access local files")
        layout.separator()
        layout.label(text="Only proceed if you trust this server.", icon="QUESTION")

class BLENDPRO_OT_ShowCode(bpy.types.Operator):
    bl_idname = "blendpro.show_code"
    bl_label = "Show Code"
    bl_options = {'REGISTER', 'UNDO'}

    code: bpy.props.StringProperty(
        name="Code",
        description="The generated code",
        default="",
    )

    def execute(self, context):
        text_name = "BLENDPRO_Generated_Code.py"
        text = bpy.data.texts.get(text_name)
        if text is None:
            text = bpy.data.texts.new(text_name)

        text.clear()
        text.write(self.code)

        text_editor_area = None
        for area in context.screen.areas:
            if area.type == 'TEXT_EDITOR':
                text_editor_area = area
                break

        if text_editor_area is None:
            text_editor_area = split_area_to_text_editor(context)

        text_editor_area.spaces.active.text = text

        return {'FINISHED'}

class BLENDPRO_PT_Panel(bpy.types.Panel):
    bl_label = "BlendPro Blender Assistant"
    bl_idname = "BLENDPRO_PT_Panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'BlendPro Assistant'

    def draw(self, context):
        layout = self.layout
        column = layout.column(align=True)

        column.label(text="Chat history:")
        box = column.box()
        for index, message in enumerate(context.scene.blendpro_chat_history):
            if message.type == 'assistant':
                row = box.row()
                row.label(text="Assistant: ")
                show_code_op = row.operator("blendpro.show_code", text="Show Code")
                show_code_op.code = message.content
                delete_message_op = row.operator("blendpro.delete_message", text="", icon="TRASH", emboss=False)
                delete_message_op.message_index = index
            else:
                row = box.row()
                row.label(text=f"User: {message.content}")
                delete_message_op = row.operator("blendpro.delete_message", text="", icon="TRASH", emboss=False)
                delete_message_op.message_index = index

        column.separator()



        # Get addon preferences to check if custom model is being used
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        if addon_prefs.use_custom_model and addon_prefs.custom_model:
            column.label(text=f"Model: {addon_prefs.custom_model} (Custom)")
        else:
            column.label(text="GPT Model:")
            column.prop(context.scene, "blendpro_model", text="")

        column.label(text="Enter your message:")
        column.prop(context.scene, "blendpro_chat_input", text="")
        button_label = "Please wait...(this might take some time)" if context.scene.blendpro_button_pressed else "Execute"
        row = column.row(align=True)
        row.operator("blendpro.send_message", text=button_label)
        row.operator("blendpro.clear_chat", text="Clear Chat")

        # MCP Status Section
        self._draw_mcp_status(context, column, addon_prefs)

        # Export/Import buttons
        row = column.row(align=True)
        row.operator("blendpro.export_chat", text="Export Chat", icon="EXPORT")
        row.operator("blendpro.import_chat", text="Import Chat", icon="IMPORT")

        # Undo/Backup buttons
        row = column.row(align=True)
        row.operator("blendpro.undo_operation", text="Undo Last", icon="LOOP_BACK")
        row.operator("blendpro.show_backups", text="Show Backups", icon="FILE_BACKUP")
        row.operator("blendpro.cleanup_backups", text="Cleanup", icon="TRASH")

        column.separator()

    def _draw_mcp_status(self, context, column, addon_prefs):
        """Draw MCP status information in the main panel"""
        if not addon_prefs.mcp_enabled:
            return

        # MCP Status Box
        mcp_box = column.box()
        mcp_header = mcp_box.row()
        mcp_header.label(text="MCP Status:", icon="NETWORK_DRIVE")

        if addon_prefs.mcp_config_mode == "legacy":
            # Legacy single server status
            if addon_prefs.mcp_server_url:
                status_row = mcp_box.row()
                status_row.label(text=f"Server: {addon_prefs.mcp_server_url[:30]}...")
                status_row.label(text="Legacy Mode", icon="INFO")
            else:
                mcp_box.label(text="No server configured", icon="ERROR")

        elif addon_prefs.mcp_config_mode == "json":
            # JSON configuration mode status
            try:
                server_manager = getattr(bpy.types.Scene, '_blendpro_server_manager', None)
                if server_manager:
                    connections = server_manager.list_servers()

                    if connections:
                        active_count = sum(1 for conn in connections.values() if conn.is_connected)
                        total_count = len(connections)

                        status_row = mcp_box.row()
                        if active_count > 0:
                            status_row.label(text=f"Servers: {active_count}/{total_count} active", icon="CHECKMARK")
                        else:
                            status_row.label(text=f"Servers: {total_count} configured, none active", icon="ERROR")

                        # Show capabilities summary
                        try:
                            capabilities = server_manager.get_aggregated_capabilities()
                            if capabilities.tools or capabilities.resources:
                                caps_row = mcp_box.row()
                                caps_row.label(text=f"Tools: {len(capabilities.tools)}, Resources: {len(capabilities.resources)}")
                        except:
                            pass

                        # Quick action buttons
                        if active_count == 0 and total_count > 0:
                            mcp_box.operator("blendpro.start_all_mcp_servers", text="Start All Servers", icon="PLAY")
                        elif active_count > 0:
                            action_row = mcp_box.row(align=True)
                            action_row.operator("blendpro.refresh_mcp_status", text="Refresh", icon="FILE_REFRESH")
                            action_row.operator("blendpro.stop_all_mcp_servers", text="Stop All", icon="PAUSE")
                    else:
                        mcp_box.label(text="No servers configured", icon="INFO")
                        if addon_prefs.mcp_config_file:
                            mcp_box.operator("blendpro.load_mcp_config", text="Load Configuration", icon="FILE_FOLDER")
                        else:
                            mcp_box.operator("blendpro.create_example_config", text="Create Example Config", icon="ADD")
                else:
                    mcp_box.label(text="Server manager not initialized", icon="ERROR")
                    if addon_prefs.mcp_config_file:
                        mcp_box.operator("blendpro.load_mcp_config", text="Initialize", icon="PLAY")
            except Exception as e:
                mcp_box.label(text=f"Status error: {str(e)[:30]}...", icon="ERROR")

        column.separator()

class BLENDPRO_OT_ClearChat(bpy.types.Operator):
    bl_idname = "blendpro.clear_chat"
    bl_label = "Clear Chat"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        context.scene.blendpro_chat_history.clear()
        # Auto-save empty chat history
        from .utilities import save_chat_history
        save_chat_history(context.scene.blendpro_chat_history)
        return {'FINISHED'}

class BLENDPRO_OT_ExportChat(bpy.types.Operator):
    bl_idname = "blendpro.export_chat"
    bl_label = "Export Chat History"
    bl_options = {'REGISTER', 'UNDO'}

    filepath: bpy.props.StringProperty(
        name="File Path",
        description="Choose a file path to export chat history",
        default="blendpro_chat_export.json",
        subtype='FILE_PATH'
    )

    def execute(self, context):
        try:
            import json
            history_data = []

            for message in context.scene.blendpro_chat_history:
                history_data.append({
                    "type": message.type,
                    "content": message.content
                })

            with open(self.filepath, 'w', encoding='utf-8') as f:
                json.dump(history_data, f, indent=2, ensure_ascii=False)

            self.report({'INFO'}, f"Chat history exported to: {self.filepath}")
            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error exporting chat history: {e}")
            return {'CANCELLED'}

    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

class BLENDPRO_OT_ImportChat(bpy.types.Operator):
    bl_idname = "blendpro.import_chat"
    bl_label = "Import Chat History"
    bl_options = {'REGISTER', 'UNDO'}

    filepath: bpy.props.StringProperty(
        name="File Path",
        description="Choose a file path to import chat history",
        default="",
        subtype='FILE_PATH'
    )

    def execute(self, context):
        try:
            import json

            with open(self.filepath, 'r', encoding='utf-8') as f:
                history_data = json.load(f)

            # Clear existing history
            context.scene.blendpro_chat_history.clear()

            # Load imported history
            for item in history_data:
                message = context.scene.blendpro_chat_history.add()
                message.type = item.get("type", "user")
                message.content = item.get("content", "")

            # Auto-save imported history
            from .utilities import save_chat_history
            save_chat_history(context.scene.blendpro_chat_history)

            self.report({'INFO'}, f"Chat history imported from: {self.filepath}")
            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error importing chat history: {e}")
            return {'CANCELLED'}

    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

class BLENDPRO_OT_CodePreview(bpy.types.Operator):
    bl_idname = "blendpro.code_preview"
    bl_label = "Code Preview"
    bl_options = {'REGISTER', 'UNDO'}

    code: bpy.props.StringProperty(
        name="Generated Code",
        description="The generated Python code to preview",
        default=""
    )

    def execute(self, context):
        # Save scene state before executing code
        from .utilities import save_scene_state, cleanup_old_backups
        backup_path = save_scene_state()

        # Execute the code
        try:
            global_namespace = globals().copy()
            exec(self.code, global_namespace)
            self.report({'INFO'}, "Code executed successfully! (Backup saved for undo)")

            # Clean up old backups periodically
            cleanup_old_backups(10)

            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error executing code: {e}")
            return {'CANCELLED'}

    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self, width=600)

    def draw(self, context):
        layout = self.layout
        layout.label(text="Generated Code Preview:", icon="FILE_SCRIPT")

        # Create a scrollable text area
        box = layout.box()
        lines = self.code.split('\n')

        # Show first 20 lines with line numbers
        for i, line in enumerate(lines[:20]):
            row = box.row()
            row.alignment = 'LEFT'
            row.label(text=f"{i+1:3d}: {line}")

        if len(lines) > 20:
            box.label(text=f"... and {len(lines) - 20} more lines")

        layout.separator()
        layout.label(text="Do you want to execute this code?", icon="QUESTION")

class BLENDPRO_OT_UndoOperation(bpy.types.Operator):
    bl_idname = "blendpro.undo_operation"
    bl_label = "Undo Last Operation"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        try:
            from .utilities import get_recent_backups
            backups = get_recent_backups(2)  # Get last 2 backups

            if len(backups) >= 2:
                # Load the previous backup (second most recent)
                backup_path = backups[1]
                bpy.ops.wm.open_mainfile(filepath=backup_path)
                self.report({'INFO'}, f"Undone to previous state: {os.path.basename(backup_path)}")
                return {'FINISHED'}
            else:
                self.report({'WARNING'}, "No previous state available for undo")
                return {'CANCELLED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error during undo: {e}")
            return {'CANCELLED'}

class BLENDPRO_OT_ShowBackups(bpy.types.Operator):
    bl_idname = "blendpro.show_backups"
    bl_label = "Show Recent Backups"
    bl_options = {'REGISTER'}

    def execute(self, context):
        from .utilities import get_recent_backups
        backups = get_recent_backups(10)

        if backups:
            self.report({'INFO'}, f"Found {len(backups)} recent backups")
            for i, backup in enumerate(backups):
                print(f"{i+1}. {os.path.basename(backup)} - {os.path.getmtime(backup)}")
        else:
            self.report({'INFO'}, "No backups found")

        return {'FINISHED'}

class BLENDPRO_OT_CleanupBackups(bpy.types.Operator):
    bl_idname = "blendpro.cleanup_backups"
    bl_label = "Cleanup Old Backups"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            from .utilities import cleanup_old_backups
            cleanup_old_backups(5)  # Keep only 5 most recent
            self.report({'INFO'}, "Old backups cleaned up")
            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error cleaning backups: {e}")
            return {'CANCELLED'}

class BLENDPRO_OT_Execute(bpy.types.Operator):
    bl_idname = "blendpro.send_message"
    bl_label = "Send Message"
    bl_options = {'REGISTER', 'UNDO'}

    natural_language_input: bpy.props.StringProperty(
        name="Command",
        description="Enter the natural language command",
        default="",
    )

    # Background processing variables
    _timer = None
    _thread = None
    _result = None
    _error = None
    _processing = False

    def execute(self, context):
        # Check if already processing
        if self._processing:
            self.report({'WARNING'}, "Already processing a request. Please wait...")
            return {'CANCELLED'}

        # Get preferences
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        # Get API key from addon preferences or environment
        api_key = addon_prefs.api_key
        if not api_key:
            api_key = os.getenv("OPENAI_API_KEY")

        if not api_key:
            self.report({'ERROR'}, "No API key detected. Please set the API key in the addon preferences.")
            return {'CANCELLED'}

        # Get base URL from preferences or environment
        base_url = addon_prefs.base_url
        if not base_url:
            base_url = os.getenv("OPENAI_BASE_URL")

        # Get model selection
        model = None
        if addon_prefs.use_custom_model and addon_prefs.custom_model:
            model = addon_prefs.custom_model

        # Store user input and add to chat history
        user_input = context.scene.blendpro_chat_input
        if not user_input.strip():
            self.report({'ERROR'}, "Please enter a message.")
            return {'CANCELLED'}

        message = context.scene.blendpro_chat_history.add()
        message.type = 'user'
        message.content = user_input

        # Auto-save chat history after adding user message
        from .utilities import save_chat_history
        save_chat_history(context.scene.blendpro_chat_history)

        # Clear the chat input field
        context.scene.blendpro_chat_input = ""

        # Set processing state
        context.scene.blendpro_button_pressed = True
        self._processing = True
        self._result = None
        self._error = None

        # Start background processing
        import threading
        self._thread = threading.Thread(
            target=self._background_process,
            args=(user_input, context.scene.blendpro_chat_history, context, api_key, base_url, model)
        )
        self._thread.daemon = True
        self._thread.start()

        # Start timer to check for completion
        wm = context.window_manager
        self._timer = wm.event_timer_add(0.1, window=context.window)
        wm.modal_handler_add(self)

        return {'RUNNING_MODAL'}

    def _background_process(self, user_input, chat_history, context, api_key, base_url, model):
        """Background thread function for API call"""
        try:
            # Get addon preferences for AI configuration
            preferences = context.preferences
            addon_prefs = preferences.addons[__name__].preferences

            # Use standard generation
            from .utilities import generate_blender_code
            result = generate_blender_code(
                user_input,
                chat_history,
                context,
                system_prompt,
                api_key,
                base_url,
                model,
                timeout=60,  # 60 second timeout
                temperature=addon_prefs.temperature,
                max_tokens=addon_prefs.max_tokens,
                top_p=addon_prefs.top_p
            )
            self._result = result
        except Exception as e:
            self._error = f"Background processing error: {str(e)}"

    def modal(self, context, event):
        """Modal handler to check background process completion"""
        if event.type == 'TIMER':
            # Check if thread is still alive
            if self._thread and self._thread.is_alive():
                return {'PASS_THROUGH'}

            # Thread completed, process results
            wm = context.window_manager
            wm.event_timer_remove(self._timer)
            self._timer = None

            context.scene.blendpro_button_pressed = False
            self._processing = False

            # Handle errors
            if self._error:
                self.report({'ERROR'}, self._error)
                return {'CANCELLED'}

            if not self._result:
                self.report({'ERROR'}, "No response received from API")
                return {'CANCELLED'}

            # Check for API errors
            if self._result.get('error'):
                error_msg = self._result['error']
                if 'timeout' in error_msg.lower():
                    self.report({'ERROR'}, "Request timed out. Please try again.")
                elif 'rate limit' in error_msg.lower():
                    self.report({'ERROR'}, "API rate limit exceeded. Please wait and try again.")
                elif 'connection' in error_msg.lower():
                    self.report({'ERROR'}, "Connection error. Please check your internet connection.")
                else:
                    self.report({'ERROR'}, f"API Error: {error_msg}")
                return {'CANCELLED'}

            # Process successful result
            blender_code = self._result.get('code')
            if blender_code and blender_code.strip():
                # Add assistant response to chat history
                message = context.scene.blendpro_chat_history.add()
                message.type = 'assistant'
                message.content = blender_code

                # Auto-save chat history after adding assistant response
                from .utilities import save_chat_history
                save_chat_history(context.scene.blendpro_chat_history)

                # Show code preview instead of direct execution
                bpy.ops.blendpro.code_preview('INVOKE_DEFAULT', code=blender_code)
            else:
                # More detailed error reporting
                error_details = self._result.get('error', 'Unknown error')
                self.report({'ERROR'}, f"No code generated from API response. Details: {error_details}")
                print(f"Full API result: {self._result}")  # Debug output
                return {'CANCELLED'}

            return {'FINISHED'}

        return {'PASS_THROUGH'}


class BLENDPRO_OT_TestConnection(bpy.types.Operator):
    bl_idname = "blendpro.test_connection"
    bl_label = "Test API Connection"
    bl_options = {'REGISTER'}

    def execute(self, context):
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        api_key = addon_prefs.api_key
        if not api_key:
            api_key = os.getenv("OPENAI_API_KEY")

        if not api_key:
            self.report({'ERROR'}, "No API key found")
            return {'CANCELLED'}

        base_url = addon_prefs.base_url
        if not base_url:
            base_url = os.getenv("OPENAI_BASE_URL")

        model = addon_prefs.custom_model if addon_prefs.use_custom_model else context.scene.blendpro_model

        from .utilities import test_openrouter_connection
        result = test_openrouter_connection(api_key, base_url, model)

        if result["success"]:
            self.report({'INFO'}, f"Connection successful! Response: {result['content'][:50]}...")
        else:
            self.report({'ERROR'}, f"Connection failed: {result['error']}")

        return {'FINISHED'}


class BLENDPRO_OT_TestMCPConnection(bpy.types.Operator):
    bl_idname = "blendpro.test_mcp_connection"
    bl_label = "Test MCP Connection"
    bl_options = {'REGISTER'}

    def execute(self, context):
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        if not addon_prefs.mcp_enabled:
            self.report({'ERROR'}, "MCP is not enabled")
            return {'CANCELLED'}

        if not addon_prefs.mcp_server_url:
            self.report({'ERROR'}, "MCP server URL is not set")
            return {'CANCELLED'}

        # Check if this is an external server (not localhost)
        server_url = addon_prefs.mcp_server_url
        if (server_url.startswith('http://') and not server_url.startswith('http://localhost') and
            not server_url.startswith('http://127.0.0.1')):
            # Show security confirmation for external servers
            bpy.ops.blendpro.confirm_mcp_connection('INVOKE_DEFAULT', server_url=server_url)
            # Note: The actual test will need to be called after confirmation
            # For now, we'll proceed with the test
            pass

        try:
            from .mcp_client import test_mcp_connection

            result = test_mcp_connection(
                addon_prefs.mcp_server_url,
                addon_prefs.mcp_transport_type,
                addon_prefs.mcp_auth_token if addon_prefs.mcp_auth_token else None
            )

            if result["success"]:
                tools_count = result.get("tools_count", 0)
                resources_count = result.get("resources_count", 0)
                self.report({'INFO'}, f"MCP connection successful! Found {tools_count} tools, {resources_count} resources")
            else:
                self.report({'ERROR'}, f"MCP connection failed: {result['error']}")

        except Exception as e:
            self.report({'ERROR'}, f"MCP test error: {str(e)}")

        return {'FINISHED'}


class BLENDPRO_OT_DiscoverMCPCapabilities(bpy.types.Operator):
    bl_idname = "blendpro.discover_mcp_capabilities"
    bl_label = "Discover MCP Capabilities"
    bl_options = {'REGISTER'}

    def execute(self, context):
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        if not addon_prefs.mcp_enabled:
            self.report({'ERROR'}, "MCP is not enabled")
            return {'CANCELLED'}

        if not addon_prefs.mcp_server_url:
            self.report({'ERROR'}, "MCP server URL is not set")
            return {'CANCELLED'}

        try:
            from .mcp_client import MCPClient

            client = MCPClient(
                addon_prefs.mcp_server_url,
                addon_prefs.mcp_transport_type,
                addon_prefs.mcp_auth_token if addon_prefs.mcp_auth_token else None,
                addon_prefs.mcp_timeout
            )

            if client.connect():
                capabilities = client.discover_capabilities()
                client.disconnect()

                # Print capabilities to console for detailed view
                print("=== MCP Server Capabilities ===")
                print(f"Tools ({len(capabilities.get('tools', []))}): ")
                for tool in capabilities.get('tools', []):
                    print(f"  - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")

                print(f"Resources ({len(capabilities.get('resources', []))}): ")
                for resource in capabilities.get('resources', []):
                    print(f"  - {resource.get('uri', 'Unknown')}: {resource.get('description', 'No description')}")

                tools_count = len(capabilities.get('tools', []))
                resources_count = len(capabilities.get('resources', []))
                prompts_count = len(capabilities.get('prompts', []))

                self.report({'INFO'}, f"Capabilities discovered: {tools_count} tools, {resources_count} resources, {prompts_count} prompts (see console for details)")
            else:
                self.report({'ERROR'}, "Failed to connect to MCP server")

        except Exception as e:
            self.report({'ERROR'}, f"MCP discovery error: {str(e)}")

        return {'FINISHED'}


def menu_func(self, context):
    self.layout.operator(BLENDPRO_OT_Execute.bl_idname)


# New MCP JSON Configuration Operators
class BLENDPRO_OT_LoadMCPConfig(bpy.types.Operator):
    bl_idname = "blendpro.load_mcp_config"
    bl_label = "Load MCP Configuration"
    bl_options = {'REGISTER'}

    def execute(self, context):
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        if not addon_prefs.mcp_config_file:
            self.report({'ERROR'}, "No configuration file specified")
            return {'CANCELLED'}

        try:
            from .mcp_config_parser import MCPConfigParser
            from .mcp_server_manager import MCPServerManager

            parser = MCPConfigParser()
            config = parser.load_from_file(addon_prefs.mcp_config_file)

            # Initialize server manager if not exists
            if not hasattr(bpy.types.Scene, '_blendpro_server_manager'):
                def log_callback(level, message):
                    print(f"MCP [{level}]: {message}")

                bpy.types.Scene._blendpro_server_manager = MCPServerManager(log_callback)

            # Set configuration
            bpy.types.Scene._blendpro_server_manager.set_configuration(config)

            server_count = len(config.servers)
            self.report({'INFO'}, f"Configuration loaded: {server_count} servers")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Failed to load configuration: {str(e)}")
            return {'CANCELLED'}


class BLENDPRO_OT_SaveMCPConfig(bpy.types.Operator):
    bl_idname = "blendpro.save_mcp_config"
    bl_label = "Save MCP Configuration"
    bl_options = {'REGISTER'}

    def execute(self, context):
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        if not addon_prefs.mcp_config_file:
            self.report({'ERROR'}, "No configuration file specified")
            return {'CANCELLED'}

        try:
            server_manager = getattr(bpy.types.Scene, '_blendpro_server_manager', None)
            if not server_manager or not server_manager.config:
                self.report({'ERROR'}, "No configuration to save")
                return {'CANCELLED'}

            from .mcp_config_parser import MCPConfigParser
            parser = MCPConfigParser()
            parser.save_to_file(server_manager.config, addon_prefs.mcp_config_file)

            self.report({'INFO'}, "Configuration saved successfully")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Failed to save configuration: {str(e)}")
            return {'CANCELLED'}


class BLENDPRO_OT_CreateExampleConfig(bpy.types.Operator):
    bl_idname = "blendpro.create_example_config"
    bl_label = "Create Example Configuration"
    bl_options = {'REGISTER'}

    def execute(self, context):
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        try:
            from .mcp_config_parser import MCPConfigParser, get_default_config_path

            # Use specified path or default
            config_path = addon_prefs.mcp_config_file or get_default_config_path()

            parser = MCPConfigParser()
            example_config = parser.create_example_config()
            parser.save_to_file(example_config, config_path)

            # Update preferences with the path
            addon_prefs.mcp_config_file = config_path

            self.report({'INFO'}, f"Example configuration created: {config_path}")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Failed to create example configuration: {str(e)}")
            return {'CANCELLED'}


class BLENDPRO_OT_StartAllMCPServers(bpy.types.Operator):
    bl_idname = "blendpro.start_all_mcp_servers"
    bl_label = "Start All MCP Servers"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            server_manager = getattr(bpy.types.Scene, '_blendpro_server_manager', None)
            if not server_manager:
                self.report({'ERROR'}, "Server manager not initialized")
                return {'CANCELLED'}

            results = server_manager.start_all_servers()

            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)

            if success_count == total_count:
                self.report({'INFO'}, f"All {total_count} servers started successfully")
            else:
                self.report({'WARNING'}, f"{success_count}/{total_count} servers started successfully")

            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Failed to start servers: {str(e)}")
            return {'CANCELLED'}


class BLENDPRO_OT_StopAllMCPServers(bpy.types.Operator):
    bl_idname = "blendpro.stop_all_mcp_servers"
    bl_label = "Stop All MCP Servers"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            server_manager = getattr(bpy.types.Scene, '_blendpro_server_manager', None)
            if not server_manager:
                self.report({'INFO'}, "No servers to stop")
                return {'FINISHED'}

            server_manager.stop_all_servers()
            self.report({'INFO'}, "All servers stopped")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Failed to stop servers: {str(e)}")
            return {'CANCELLED'}


class BLENDPRO_OT_RefreshMCPStatus(bpy.types.Operator):
    bl_idname = "blendpro.refresh_mcp_status"
    bl_label = "Refresh MCP Status"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            server_manager = getattr(bpy.types.Scene, '_blendpro_server_manager', None)
            if not server_manager:
                self.report({'INFO'}, "No server manager initialized")
                return {'FINISHED'}

            # Force refresh capabilities
            capabilities = server_manager.get_aggregated_capabilities(force_refresh=True)

            # Get server status
            connections = server_manager.list_servers()
            active_count = sum(1 for conn in connections.values() if conn.is_connected)

            self.report({'INFO'}, f"Status refreshed: {active_count}/{len(connections)} servers active")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Failed to refresh status: {str(e)}")
            return {'CANCELLED'}


class BLENDPRO_OT_ShowMCPServers(bpy.types.Operator):
    bl_idname = "blendpro.show_mcp_servers"
    bl_label = "Show Server Status"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            server_manager = getattr(bpy.types.Scene, '_blendpro_server_manager', None)
            if not server_manager:
                self.report({'INFO'}, "No servers configured")
                return {'FINISHED'}

            connections = server_manager.list_servers()

            if not connections:
                self.report({'INFO'}, "No servers found")
                return {'FINISHED'}

            print("=== MCP Server Status ===")
            for name, connection in connections.items():
                status = connection.status_summary
                uptime = ""
                if connection.process_info and connection.process_info.uptime > 0:
                    uptime = f" (uptime: {connection.process_info.uptime:.1f}s)"

                capabilities_info = ""
                if connection.capabilities:
                    tools = len(connection.capabilities.tools)
                    resources = len(connection.capabilities.resources)
                    capabilities_info = f" - {tools} tools, {resources} resources"

                print(f"  {name}: {status}{uptime}{capabilities_info}")

                if connection.last_error:
                    print(f"    Error: {connection.last_error}")

            self.report({'INFO'}, f"Server status shown in console ({len(connections)} servers)")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Failed to show server status: {str(e)}")
            return {'CANCELLED'}


class BLENDPRO_OT_OpenMCPLogs(bpy.types.Operator):
    bl_idname = "blendpro.open_mcp_logs"
    bl_label = "Open MCP Logs"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            import tempfile
            import os

            # Get temp directory where logs might be stored
            temp_dir = tempfile.gettempdir()
            log_dir = os.path.join(temp_dir, "blendpro_mcp_logs")

            if os.path.exists(log_dir):
                # Open log directory in file explorer
                if sys.platform == "win32":
                    os.startfile(log_dir)
                elif sys.platform == "darwin":
                    subprocess.run(["open", log_dir])
                else:
                    subprocess.run(["xdg-open", log_dir])

                self.report({'INFO'}, f"Opened log directory: {log_dir}")
            else:
                self.report({'INFO'}, "No log directory found")

            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Failed to open logs: {str(e)}")
            return {'CANCELLED'}


class BLENDPRO_OT_ResetMCPConfig(bpy.types.Operator):
    bl_idname = "blendpro.reset_mcp_config"
    bl_label = "Reset MCP Configuration"
    bl_options = {'REGISTER'}

    def invoke(self, context, event):
        return context.window_manager.invoke_confirm(self, event)

    def execute(self, context):
        try:
            # Stop all servers first
            server_manager = getattr(bpy.types.Scene, '_blendpro_server_manager', None)
            if server_manager:
                server_manager.stop_all_servers()
                server_manager.cleanup()
                delattr(bpy.types.Scene, '_blendpro_server_manager')

            # Reset preferences to defaults
            preferences = context.preferences
            addon_prefs = preferences.addons[__name__].preferences

            addon_prefs.mcp_config_mode = "legacy"
            addon_prefs.mcp_config_file = ""
            addon_prefs.mcp_auto_start = False
            addon_prefs.mcp_show_advanced = False

            self.report({'INFO'}, "MCP configuration reset to defaults")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Failed to reset configuration: {str(e)}")
            return {'CANCELLED'}

class BLENDPROAddonPreferences(bpy.types.AddonPreferences):
    bl_idname = __name__

    api_key: bpy.props.StringProperty(
        name="API Key",
        description="Enter your OpenAI API Key",
        default="",
        subtype="PASSWORD",
    )

    base_url: bpy.props.StringProperty(
        name="Base URL",
        description="OpenAI API Base URL (leave empty for default: https://api.openai.com/v1)",
        default="",
    )

    custom_model: bpy.props.StringProperty(
        name="Custom Model",
        description="Custom model ID (e.g., gpt-4-turbo, claude-3-opus, local-model)",
        default="",
    )

    use_custom_model: bpy.props.BoolProperty(
        name="Use Custom Model",
        description="Use custom model instead of predefined ones",
        default=False,
    )

    # AI Configuration Parameters
    temperature: bpy.props.FloatProperty(
        name="Temperature",
        description="Controls randomness in the output (0.0 = deterministic, 2.0 = very random)",
        default=0.7,
        min=0.0,
        max=2.0,
        step=0.1,
        precision=1,
    )

    max_tokens: bpy.props.IntProperty(
        name="Max Tokens",
        description="Maximum number of tokens to generate in the response",
        default=1500,
        min=1,
        max=4000,
    )

    top_p: bpy.props.FloatProperty(
        name="Top P",
        description="Controls diversity via nucleus sampling (0.1 = only top 10% of tokens)",
        default=1.0,
        min=0.0,
        max=1.0,
        step=0.1,
        precision=2,
    )

    # MCP Configuration Parameters
    mcp_server_url: bpy.props.StringProperty(
        name="MCP Server URL",
        description="URL of the MCP server (e.g., http://localhost:8080) or command for stdio",
        default="",
    )

    mcp_transport_type: bpy.props.EnumProperty(
        name="Transport Type",
        description="MCP transport method",
        items=[
            ("http", "HTTP+SSE", "HTTP with Server-Sent Events"),
            ("stdio", "Stdio", "Standard input/output (subprocess)"),
        ],
        default="http",
    )

    mcp_auth_token: bpy.props.StringProperty(
        name="Auth Token",
        description="Optional authentication token for MCP server",
        default="",
        subtype="PASSWORD",
    )

    mcp_enabled: bpy.props.BoolProperty(
        name="Enable MCP",
        description="Enable Model Context Protocol integration",
        default=False,
    )

    mcp_timeout: bpy.props.IntProperty(
        name="MCP Timeout",
        description="Request timeout in seconds",
        default=30,
        min=5,
        max=120,
    )

    # New MCP Configuration Mode
    mcp_config_mode: bpy.props.EnumProperty(
        name="Configuration Mode",
        description="MCP configuration method",
        items=[
            ("legacy", "Legacy Single Server", "Use single server configuration (backward compatible)"),
            ("json", "JSON Configuration", "Use Claude Desktop compatible JSON configuration"),
        ],
        default="legacy",
    )

    mcp_config_file: bpy.props.StringProperty(
        name="Config File Path",
        description="Path to MCP JSON configuration file",
        default="",
        subtype="FILE_PATH",
    )

    mcp_auto_start: bpy.props.BoolProperty(
        name="Auto-start Servers",
        description="Automatically start MCP servers when Blender starts",
        default=False,
    )

    mcp_show_advanced: bpy.props.BoolProperty(
        name="Show Advanced Options",
        description="Show advanced MCP configuration options",
        default=False,
    )

    def draw(self, context):
        layout = self.layout
        layout.prop(self, "api_key")
        layout.separator()
        layout.prop(self, "base_url")
        layout.separator()
        layout.prop(self, "use_custom_model")
        if self.use_custom_model:
            layout.prop(self, "custom_model")
        layout.separator()

        # AI Configuration Section
        box = layout.box()
        box.label(text="AI Configuration:", icon="SETTINGS")
        box.prop(self, "temperature")
        box.prop(self, "max_tokens")
        box.prop(self, "top_p")

        layout.separator()

        # MCP Configuration Section
        mcp_box = layout.box()
        mcp_box.label(text="MCP (Model Context Protocol):", icon="NETWORK_DRIVE")
        mcp_box.prop(self, "mcp_enabled")

        if self.mcp_enabled:
            # Configuration mode selection
            mcp_box.prop(self, "mcp_config_mode")

            if self.mcp_config_mode == "legacy":
                # Legacy single server configuration
                legacy_box = mcp_box.box()
                legacy_box.label(text="Legacy Single Server:", icon="SETTINGS")
                legacy_box.prop(self, "mcp_server_url")
                legacy_box.prop(self, "mcp_transport_type")
                if self.mcp_transport_type == "http":
                    legacy_box.prop(self, "mcp_auth_token")
                legacy_box.prop(self, "mcp_timeout")

                # Legacy test buttons
                legacy_row = legacy_box.row(align=True)
                legacy_row.operator("blendpro.test_mcp_connection", text="Test Connection")
                legacy_row.operator("blendpro.discover_mcp_capabilities", text="Discover Capabilities")

            elif self.mcp_config_mode == "json":
                # JSON configuration mode
                json_box = mcp_box.box()
                json_box.label(text="JSON Configuration (Claude Desktop Compatible):", icon="FILE_TEXT")
                json_box.prop(self, "mcp_config_file")
                json_box.prop(self, "mcp_auto_start")

                # JSON management buttons
                json_row = json_box.row(align=True)
                json_row.operator("blendpro.load_mcp_config", text="Load Config")
                json_row.operator("blendpro.save_mcp_config", text="Save Config")
                json_row.operator("blendpro.create_example_config", text="Create Example")

                # Server management
                servers_box = json_box.box()
                servers_box.label(text="Server Management:", icon="PREFERENCES")
                servers_row = servers_box.row(align=True)
                servers_row.operator("blendpro.start_all_mcp_servers", text="Start All")
                servers_row.operator("blendpro.stop_all_mcp_servers", text="Stop All")
                servers_row.operator("blendpro.refresh_mcp_status", text="Refresh")

                # Server list (will be populated by operator)
                servers_box.operator("blendpro.show_mcp_servers", text="Show Server Status")

            # Advanced options
            mcp_box.prop(self, "mcp_show_advanced")
            if self.mcp_show_advanced:
                adv_box = mcp_box.box()
                adv_box.label(text="Advanced Options:", icon="TOOL_SETTINGS")
                adv_box.operator("blendpro.open_mcp_logs", text="Open Logs")
                adv_box.operator("blendpro.reset_mcp_config", text="Reset Configuration")

        layout.separator()
        layout.operator("blendpro.test_connection", text="Test API Connection")

def register():
    bpy.utils.register_class(BLENDPROAddonPreferences)
    bpy.utils.register_class(BLENDPRO_OT_Execute)
    bpy.utils.register_class(BLENDPRO_PT_Panel)
    bpy.utils.register_class(BLENDPRO_OT_ClearChat)
    bpy.utils.register_class(BLENDPRO_OT_ExportChat)
    bpy.utils.register_class(BLENDPRO_OT_ImportChat)
    bpy.utils.register_class(BLENDPRO_OT_CodePreview)
    bpy.utils.register_class(BLENDPRO_OT_UndoOperation)
    bpy.utils.register_class(BLENDPRO_OT_ShowBackups)
    bpy.utils.register_class(BLENDPRO_OT_CleanupBackups)
    bpy.utils.register_class(BLENDPRO_OT_ShowCode)
    bpy.utils.register_class(BLENDPRO_OT_DeleteMessage)
    bpy.utils.register_class(BLENDPRO_OT_ConfirmMCPConnection)
    bpy.utils.register_class(BLENDPRO_OT_TestConnection)
    bpy.utils.register_class(BLENDPRO_OT_TestMCPConnection)
    bpy.utils.register_class(BLENDPRO_OT_DiscoverMCPCapabilities)
    bpy.utils.register_class(BLENDPRO_OT_LoadMCPConfig)
    bpy.utils.register_class(BLENDPRO_OT_SaveMCPConfig)
    bpy.utils.register_class(BLENDPRO_OT_CreateExampleConfig)
    bpy.utils.register_class(BLENDPRO_OT_StartAllMCPServers)
    bpy.utils.register_class(BLENDPRO_OT_StopAllMCPServers)
    bpy.utils.register_class(BLENDPRO_OT_RefreshMCPStatus)
    bpy.utils.register_class(BLENDPRO_OT_ShowMCPServers)
    bpy.utils.register_class(BLENDPRO_OT_OpenMCPLogs)
    bpy.utils.register_class(BLENDPRO_OT_ResetMCPConfig)

    bpy.types.VIEW3D_MT_mesh_add.append(menu_func)
    init_props()

    # Load saved chat history on addon startup
    def load_history_delayed():
        """Load chat history with a small delay to ensure context is ready"""
        try:
            if bpy.context and bpy.context.scene:
                from .utilities import load_chat_history
                load_chat_history(bpy.context)
        except Exception as e:
            print(f"Error loading chat history on startup: {e}")

    # Use a timer to delay loading slightly
    bpy.app.timers.register(load_history_delayed, first_interval=0.1)

    # Auto-start MCP servers if enabled
    def auto_start_mcp_delayed():
        """Auto-start MCP servers with a delay to ensure context is ready"""
        try:
            if bpy.context and bpy.context.preferences:
                preferences = bpy.context.preferences
                addon_prefs = preferences.addons[__name__].preferences

                if (addon_prefs.mcp_enabled and
                    addon_prefs.mcp_config_mode == "json" and
                    addon_prefs.mcp_auto_start and
                    addon_prefs.mcp_config_file):

                    # Initialize server manager and load configuration
                    try:
                        from .mcp_config_parser import MCPConfigParser
                        from .mcp_server_manager import MCPServerManager

                        def log_callback(level, message):
                            print(f"MCP Auto-start [{level}]: {message}")

                        # Create server manager
                        if not hasattr(bpy.types.Scene, '_blendpro_server_manager'):
                            bpy.types.Scene._blendpro_server_manager = MCPServerManager(log_callback)

                        # Load configuration
                        parser = MCPConfigParser()
                        config = parser.load_from_file(addon_prefs.mcp_config_file)
                        bpy.types.Scene._blendpro_server_manager.set_configuration(config)

                        # Start servers
                        results = bpy.types.Scene._blendpro_server_manager.start_all_servers()
                        success_count = sum(1 for success in results.values() if success)
                        total_count = len(results)

                        print(f"MCP Auto-start: {success_count}/{total_count} servers started successfully")

                    except Exception as e:
                        print(f"MCP Auto-start error: {e}")
        except Exception as e:
            print(f"Error in MCP auto-start: {e}")

    # Use a timer to delay MCP auto-start
    bpy.app.timers.register(auto_start_mcp_delayed, first_interval=1.0)


def unregister():
    # Cleanup MCP servers before unregistering
    try:
        server_manager = getattr(bpy.types.Scene, '_blendpro_server_manager', None)
        if server_manager:
            server_manager.cleanup()
            delattr(bpy.types.Scene, '_blendpro_server_manager')
    except Exception as e:
        print(f"Error cleaning up MCP servers: {e}")

    bpy.utils.unregister_class(BLENDPROAddonPreferences)
    bpy.utils.unregister_class(BLENDPRO_OT_Execute)
    bpy.utils.unregister_class(BLENDPRO_PT_Panel)
    bpy.utils.unregister_class(BLENDPRO_OT_ClearChat)
    bpy.utils.unregister_class(BLENDPRO_OT_ExportChat)
    bpy.utils.unregister_class(BLENDPRO_OT_ImportChat)
    bpy.utils.unregister_class(BLENDPRO_OT_CodePreview)
    bpy.utils.unregister_class(BLENDPRO_OT_UndoOperation)
    bpy.utils.unregister_class(BLENDPRO_OT_ShowBackups)
    bpy.utils.unregister_class(BLENDPRO_OT_CleanupBackups)
    bpy.utils.unregister_class(BLENDPRO_OT_ShowCode)
    bpy.utils.unregister_class(BLENDPRO_OT_DeleteMessage)
    bpy.utils.unregister_class(BLENDPRO_OT_ConfirmMCPConnection)
    bpy.utils.unregister_class(BLENDPRO_OT_TestConnection)
    bpy.utils.unregister_class(BLENDPRO_OT_TestMCPConnection)
    bpy.utils.unregister_class(BLENDPRO_OT_DiscoverMCPCapabilities)
    bpy.utils.unregister_class(BLENDPRO_OT_LoadMCPConfig)
    bpy.utils.unregister_class(BLENDPRO_OT_SaveMCPConfig)
    bpy.utils.unregister_class(BLENDPRO_OT_CreateExampleConfig)
    bpy.utils.unregister_class(BLENDPRO_OT_StartAllMCPServers)
    bpy.utils.unregister_class(BLENDPRO_OT_StopAllMCPServers)
    bpy.utils.unregister_class(BLENDPRO_OT_RefreshMCPStatus)
    bpy.utils.unregister_class(BLENDPRO_OT_ShowMCPServers)
    bpy.utils.unregister_class(BLENDPRO_OT_OpenMCPLogs)
    bpy.utils.unregister_class(BLENDPRO_OT_ResetMCPConfig)

    bpy.types.VIEW3D_MT_mesh_add.remove(menu_func)
    clear_props()


if __name__ == "__main__":
    register()
