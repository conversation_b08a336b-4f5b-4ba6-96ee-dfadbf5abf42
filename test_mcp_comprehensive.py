"""
Comprehensive Test Suite for BlendPro MCP Integration
Tests all components of the MCP system including security, configuration, and server management.

Author: inkbytefo
"""

import sys
import os
import json
import tempfile
import time
import unittest
from unittest.mock import Mock, patch, MagicMock

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from mcp_config_parser import MCPConfigParser, MCPServerConfig, MCPConfiguration
    from mcp_security import MCPSecurityValidator, MCPSecurityError
    from mcp_process_manager import MCPProcessManager, ProcessStatus
    from mcp_server_manager import MCPServerManager
    print("✓ All MCP modules imported successfully")
except ImportError as e:
    print(f"✗ Failed to import MCP modules: {e}")
    sys.exit(1)


class TestMCPConfigParser(unittest.TestCase):
    """Test MCP Configuration Parser"""
    
    def setUp(self):
        self.parser = MCPConfigParser()
        self.temp_dir = tempfile.mkdtemp()
    
    def test_create_example_config(self):
        """Test creating example configuration"""
        config = self.parser.create_example_config()
        
        self.assertIsInstance(config, MCPConfiguration)
        self.assertGreater(len(config.servers), 0)
        self.assertIn("filesystem", config.servers)
        self.assertIn("memory", config.servers)
    
    def test_json_serialization(self):
        """Test JSON serialization and deserialization"""
        config = self.parser.create_example_config()
        
        # Convert to JSON
        json_data = self.parser.to_json(config)
        self.assertIn("mcpServers", json_data)
        self.assertIn("config_version", json_data)
        
        # Parse back from JSON
        parsed_config = self.parser.parse_json(json_data)
        self.assertEqual(len(parsed_config.servers), len(config.servers))
    
    def test_file_operations(self):
        """Test file save and load operations"""
        config = self.parser.create_example_config()
        config_path = os.path.join(self.temp_dir, "test_config.json")
        
        # Save to file
        self.parser.save_to_file(config, config_path)
        self.assertTrue(os.path.exists(config_path))
        
        # Load from file
        loaded_config = self.parser.load_from_file(config_path)
        self.assertEqual(len(loaded_config.servers), len(config.servers))
    
    def test_validation(self):
        """Test configuration validation"""
        config = self.parser.create_example_config()
        warnings = self.parser.validate_config(config)
        
        # Should have some warnings (like missing tokens)
        self.assertIsInstance(warnings, list)


class TestMCPSecurity(unittest.TestCase):
    """Test MCP Security Validator"""
    
    def setUp(self):
        self.validator = MCPSecurityValidator(strict_mode=True)
    
    def test_safe_command_validation(self):
        """Test validation of safe commands"""
        safe_config = MCPServerConfig(
            name="test_safe",
            command="npx",
            args=["-y", "@modelcontextprotocol/server-filesystem", "/safe/path"],
            timeout=30
        )
        
        # Should not raise exception
        try:
            self.validator.validate_server_config(safe_config)
        except MCPSecurityError:
            self.fail("Safe configuration should not raise security error")
    
    def test_dangerous_command_validation(self):
        """Test validation rejects dangerous commands"""
        dangerous_config = MCPServerConfig(
            name="test_dangerous",
            command="rm",
            args=["-rf", "/"],
            timeout=30
        )
        
        # Should raise exception
        with self.assertRaises(MCPSecurityError):
            self.validator.validate_server_config(dangerous_config)
    
    def test_path_traversal_detection(self):
        """Test detection of path traversal attempts"""
        traversal_config = MCPServerConfig(
            name="test_traversal",
            command="npx",
            args=["../../../etc/passwd"],
            timeout=30
        )

        # Should raise warnings or errors
        try:
            self.validator.validate_server_config(traversal_config)
            # Check if warnings were generated
            self.assertGreater(len(self.validator.warnings), 0)
        except MCPSecurityError:
            # If it raises an error, that's also acceptable
            pass
    
    def test_environment_sanitization(self):
        """Test environment variable sanitization"""
        dangerous_env = {
            "PATH": "/dangerous/path",
            "SAFE_VAR": "safe_value",
            "bad-key": "value",
            "LONG_VAR": "x" * 2000
        }
        
        sanitized = self.validator.sanitize_environment(dangerous_env)
        
        # PATH should be removed
        self.assertNotIn("PATH", sanitized)
        # Safe var should remain
        self.assertIn("SAFE_VAR", sanitized)
        # Bad key should be sanitized
        self.assertIn("BADKEY", sanitized)
        # Long value should be truncated
        self.assertLessEqual(len(sanitized.get("LONG_VAR", "")), 1000)
    
    def test_configuration_validation(self):
        """Test full configuration validation"""
        parser = MCPConfigParser()
        config = parser.create_example_config()
        
        is_valid, warnings, errors = self.validator.validate_configuration(config)
        
        # Should be valid (example config is safe)
        self.assertTrue(is_valid)
        self.assertIsInstance(warnings, list)
        self.assertIsInstance(errors, list)


class TestMCPProcessManager(unittest.TestCase):
    """Test MCP Process Manager"""
    
    def setUp(self):
        self.manager = MCPProcessManager()
    
    def tearDown(self):
        self.manager.cleanup()
    
    def test_manager_initialization(self):
        """Test process manager initialization"""
        self.assertIsNotNone(self.manager)
        self.assertEqual(len(self.manager.processes), 0)
    
    def test_echo_process(self):
        """Test starting and stopping a simple echo process"""
        test_config = MCPServerConfig(
            name="test_echo",
            command="echo",
            args=["Hello MCP"],
            timeout=10
        )
        
        # Start process
        success = self.manager.start_server(test_config)
        self.assertTrue(success)
        
        # Check status
        status = self.manager.get_server_status("test_echo")
        self.assertIsNotNone(status)
        
        # Stop process
        success = self.manager.stop_server("test_echo")
        self.assertTrue(success)
    
    @patch('subprocess.Popen')
    def test_process_monitoring(self, mock_popen):
        """Test process monitoring functionality"""
        # Mock process with context manager support
        mock_process = Mock()
        mock_process.pid = 12345
        mock_process.poll.return_value = None  # Process running
        mock_process.stdin = Mock()
        mock_process.stdout = Mock()
        mock_process.stderr = Mock()
        mock_process.__enter__ = Mock(return_value=mock_process)
        mock_process.__exit__ = Mock(return_value=None)
        mock_popen.return_value = mock_process

        test_config = MCPServerConfig(
            name="test_monitor",
            command="python",
            args=["-c", "import time; time.sleep(10)"],
            timeout=30
        )

        success = self.manager.start_server(test_config)
        self.assertTrue(success)

        status = self.manager.get_server_status("test_monitor")
        self.assertEqual(status.status, ProcessStatus.RUNNING)
        self.assertEqual(status.pid, 12345)


class TestMCPServerManager(unittest.TestCase):
    """Test MCP Server Manager"""
    
    def setUp(self):
        self.manager = MCPServerManager()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        self.manager.cleanup()
    
    def test_manager_initialization(self):
        """Test server manager initialization"""
        self.assertIsNotNone(self.manager)
        self.assertIsNotNone(self.manager.security_validator)
        self.assertEqual(len(self.manager.connections), 0)
    
    def test_configuration_loading(self):
        """Test configuration loading with validation"""
        # Create test configuration
        parser = MCPConfigParser()
        config = parser.create_example_config()
        config_path = os.path.join(self.temp_dir, "test_config.json")
        parser.save_to_file(config, config_path)
        
        # Load configuration
        success = self.manager.load_configuration(config_path)
        self.assertTrue(success)
        self.assertIsNotNone(self.manager.config)
    
    def test_security_integration(self):
        """Test security validation integration"""
        # Create configuration with dangerous command
        dangerous_config = MCPConfiguration()
        dangerous_server = MCPServerConfig(
            name="dangerous",
            command="rm",
            args=["-rf", "/"],
            timeout=30
        )
        dangerous_config.add_server(dangerous_server)
        
        # Should fail security validation
        success = self.manager.set_configuration(dangerous_config)
        self.assertFalse(success)


class TestMCPIntegration(unittest.TestCase):
    """Integration tests for complete MCP system"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
    
    def test_end_to_end_workflow(self):
        """Test complete workflow from config to server management"""
        # 1. Create configuration
        parser = MCPConfigParser()
        config = parser.create_example_config()
        
        # 2. Validate security
        validator = MCPSecurityValidator()
        is_valid, warnings, errors = validator.validate_configuration(config)
        self.assertTrue(is_valid)
        
        # 3. Save and load configuration
        config_path = os.path.join(self.temp_dir, "integration_test.json")
        parser.save_to_file(config, config_path)
        loaded_config = parser.load_from_file(config_path)
        
        # 4. Initialize server manager
        manager = MCPServerManager()
        success = manager.set_configuration(loaded_config)
        self.assertTrue(success)
        
        # 5. Cleanup
        manager.cleanup()
    
    def test_claude_desktop_compatibility(self):
        """Test compatibility with Claude Desktop configuration format"""
        claude_config = {
            "mcpServers": {
                "filesystem": {
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-filesystem", "/tmp"]
                },
                "memory": {
                    "command": "npx",
                    "args": ["-y", "@modelcontextprotocol/server-memory"]
                }
            }
        }
        
        # Parse Claude Desktop format
        parser = MCPConfigParser()
        config = parser.parse_json(claude_config)
        
        self.assertEqual(len(config.servers), 2)
        self.assertIn("filesystem", config.servers)
        self.assertIn("memory", config.servers)
        
        # Validate security
        validator = MCPSecurityValidator()
        is_valid, warnings, errors = validator.validate_configuration(config)
        self.assertTrue(is_valid)


def run_comprehensive_tests():
    """Run all MCP tests"""
    print("BlendPro MCP Comprehensive Test Suite")
    print("=" * 50)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestMCPConfigParser,
        TestMCPSecurity,
        TestMCPProcessManager,
        TestMCPServerManager,
        TestMCPIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\nOverall result: {'✓ PASSED' if success else '✗ FAILED'}")
    
    return success


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
