# BlendPro MCP Integration Guide

## Overview

BlendPro now supports advanced MCP (Model Context Protocol) integration with Claude Desktop compatibility, allowing the AI assistant to access multiple external tools and resources for enhanced Blender operations.

### 🆕 What's New in Version 3.0

- **Claude Desktop Compatibility**: Full support for Claude Desktop JSON configuration format
- **Multiple Server Support**: Connect to multiple MCP servers simultaneously
- **Enhanced Security**: Comprehensive security validation and command whitelisting
- **Auto-start Capability**: Automatically start MCP servers when <PERSON><PERSON><PERSON> launches
- **Backward Compatibility**: Existing single-server configurations continue to work
- **Advanced UI**: Improved preferences panel and main panel status indicators

## What is MCP?

Model Context Protocol (MCP) is a standardized protocol that enables AI models to securely connect to external data sources and tools. This allows BlendPro to:

- Fetch materials and textures from online databases
- Access local file systems for .blend, .obj, and other 3D files
- Query web APIs for 3D models and assets
- Integrate with external databases and services

## Configuration Modes

BlendPro supports two configuration modes:

### 1. Legacy Mode (Single Server)
For backward compatibility with existing setups.

### 2. JSON Configuration Mode (Recommended)
Claude Desktop compatible format supporting multiple servers.

## Quick Start

### Option A: JSON Configuration (Recommended)

1. **Enable MCP**: Go to Edit > Preferences > Add-ons > BlendPro
2. **Select JSON Mode**: Choose "JSON Configuration" from Configuration Mode
3. **Create Example Config**: Click "Create Example" to generate a sample configuration
4. **Load Configuration**: Click "Load Config" to initialize the server manager
5. **Start Servers**: Click "Start All" to launch your MCP servers

### Option B: Legacy Single Server

1. **Enable MCP**: Go to Edit > Preferences > Add-ons > BlendPro > Enable MCP
2. **Select Legacy Mode**: Choose "Legacy Single Server" from Configuration Mode
3. **Set Server URL**: Enter your MCP server URL (e.g., `http://localhost:8080`)
4. **Choose Transport**: Select HTTP or Stdio transport type
5. **Test Connection**: Use the "Test Connection" button

## JSON Configuration Format

BlendPro uses the same JSON format as Claude Desktop:

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/allowed/files"],
      "enabled": true,
      "timeout": 30
    },
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "enabled": true
    },
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "your_token_here"
      },
      "enabled": false
    }
  }
}
```

### Configuration Options

- **command**: Executable command (npx, python, node, etc.)
- **args**: Command arguments array
- **env**: Environment variables (optional)
- **cwd**: Working directory (optional)
- **enabled**: Whether server should start automatically
- **timeout**: Request timeout in seconds (default: 30)

## Migration Guide

### Migrating from Legacy to JSON Configuration

If you're currently using the legacy single-server configuration, here's how to migrate:

#### Step 1: Export Current Settings
1. Note your current MCP server URL and settings
2. Test that your current setup works before migrating

#### Step 2: Create JSON Configuration
1. Go to Preferences > BlendPro > MCP Configuration
2. Change "Configuration Mode" to "JSON Configuration"
3. Click "Create Example Config" to generate a template
4. Edit the generated configuration file to match your current setup

#### Step 3: Configure Your Server
Replace the example server with your current configuration:

```json
{
  "mcpServers": {
    "my_server": {
      "command": "npx",
      "args": ["-y", "your-mcp-server-package"],
      "enabled": true,
      "timeout": 30
    }
  },
  "legacy": {
    "enabled": false
  }
}
```

#### Step 4: Test New Configuration
1. Click "Load Config" to initialize the new system
2. Click "Start All" to launch servers
3. Use "Refresh" to check server status
4. Test functionality with a simple prompt

#### Step 5: Enable Auto-start (Optional)
1. Check "Auto-start Servers" in preferences
2. Servers will automatically start when Blender launches

### Rollback Plan
If you encounter issues, you can always rollback:
1. Change "Configuration Mode" back to "Legacy Single Server"
2. Your original settings will still be there
3. Click "Test Connection" to verify legacy mode works

## Security Features

### Command Validation
- Only whitelisted commands are allowed (npx, python, node, etc.)
- Dangerous commands (rm, del, sudo) are blocked
- Path traversal attempts are detected and prevented

### Environment Variable Sanitization
- System environment variables (PATH, HOME) are protected
- Variable names are validated and sanitized
- Values are checked for dangerous patterns

### Working Directory Validation
- Prevents access to system directories
- Validates directory existence
- Warns about potentially unsafe locations

### Security Modes
- **Strict Mode**: Maximum security, only known-safe commands
- **Permissive Mode**: Allows more commands with warnings

## Supported Transport Types

### HTTP+SSE (Recommended)
- **URL Format**: `http://localhost:8080` or `https://your-mcp-server.com`
- **Use Case**: Remote MCP servers, web-based services
- **Security**: Supports authentication tokens, HTTPS recommended

### Stdio
- **Command Format**: `python mcp_server.py` or `/path/to/mcp-server`
- **Use Case**: Local MCP servers, filesystem access
- **Security**: Local execution only

## Example MCP Servers

### 1. Filesystem MCP Server
```bash
# Install filesystem MCP server
pip install mcp-server-filesystem

# Run server
python -m mcp_server_filesystem /path/to/your/blender/projects
```

**Configuration in BlendPro:**
- Transport Type: Stdio
- Server URL: `python -m mcp_server_filesystem /path/to/your/blender/projects`

### 2. Web Fetch MCP Server
```bash
# Install fetch MCP server
pip install mcp-server-fetch

# Run server on port 8080
python -m mcp_server_fetch --port 8080
```

**Configuration in BlendPro:**
- Transport Type: HTTP+SSE
- Server URL: `http://localhost:8080`

## Usage Examples

### 1. Material Database Access
**User Prompt**: "Add a realistic wood material from online database"

**What happens:**
1. BlendPro detects "material" and "online" keywords
2. Connects to MCP server with fetch capability
3. Queries material database via MCP
4. Generates Blender code with material data
5. Creates material nodes in Blender

### 2. Local File Import
**User Prompt**: "Import all .obj files from my models folder"

**What happens:**
1. BlendPro detects "import" and "obj" keywords
2. Connects to filesystem MCP server
3. Lists available .obj files via MCP
4. Generates import code for each file
5. Imports models into Blender scene

### 3. Asset Search
**User Prompt**: "Find and import a medieval castle model"

**What happens:**
1. BlendPro detects search intent
2. Uses MCP web search tools
3. Finds relevant 3D models
4. Downloads and imports the best match

## Security Features

### User Consent
- First-time connections require user confirmation
- Clear indication of external server access
- Option to disable MCP at any time

### URL Validation
- HTTPS enforcement for remote servers
- Localhost exception for development
- Invalid URL rejection

### Error Handling
- Graceful fallback when MCP unavailable
- Timeout protection
- Detailed error messages

## Troubleshooting

### Connection Issues
1. **"MCP connection failed"**
   - Check server URL format
   - Verify server is running
   - Test with simple HTTP request

2. **"No tools available"**
   - Server may not support required capabilities
   - Check server documentation
   - Try capability discovery

3. **"Timeout error"**
   - Increase timeout in preferences
   - Check network connectivity
   - Verify server performance

### Performance Issues
1. **Slow responses**
   - Reduce MCP timeout
   - Use local MCP servers when possible
   - Cache frequently used resources

2. **UI freezing**
   - MCP operations run in background
   - Check console for error messages
   - Restart Blender if needed

## Advanced Configuration

### Custom MCP Servers
You can create custom MCP servers for specific workflows:

```python
# Example: Custom material server
from mcp import Server

server = Server("blender-materials")

@server.tool("get_material")
def get_material(material_type: str):
    # Your custom material logic
    return {"nodes": [...], "properties": {...}}
```

### Multiple Servers
BlendPro currently supports one MCP server at a time. To use multiple servers:
1. Use a proxy MCP server that aggregates multiple backends
2. Switch server URLs in preferences as needed
3. Consider using different transport types for different use cases

## Best Practices

1. **Start Simple**: Begin with local filesystem MCP server
2. **Test Thoroughly**: Always test MCP connection before production use
3. **Monitor Performance**: Watch for timeout and performance issues
4. **Security First**: Use HTTPS for remote servers, validate URLs
5. **Fallback Ready**: Ensure BlendPro works without MCP when needed

## API Reference

### MCP Client Methods
- `connect()`: Establish connection to MCP server
- `discover_capabilities()`: Get available tools and resources
- `execute_tool(name, params)`: Run a specific tool
- `fetch_resource(uri)`: Get resource content
- `disconnect()`: Close connection

### Blender Integration
- MCP context automatically added to AI prompts
- Keywords trigger specific MCP operations
- Results integrated into generated Blender code

## Support

For MCP integration issues:
1. Check console output for detailed errors
2. Verify MCP server compatibility
3. Test with example servers first
4. Report issues on GitHub with MCP logs

## Advanced Troubleshooting

### JSON Configuration Issues

1. **Server Manager Not Initialized**
   - **Solution**: Click "Load Config" in preferences
   - **Cause**: Configuration file not loaded

2. **Servers Won't Start**
   - **Check**: Command exists in PATH (npx, python, etc.)
   - **Check**: Arguments are correct
   - **Solution**: Use "Show Server Status" for detailed errors

3. **Security Validation Failed**
   - **Check**: Commands are in whitelist
   - **Solution**: Review security warnings in console

### Performance Optimization

1. **Multiple Servers**: Start only needed servers
2. **Caching**: Capabilities cached for 5 minutes
3. **Auto-start**: Use only for frequently used servers

### Debug Tools

- **Console Logging**: Check Blender console for MCP logs
- **Server Status**: Use "Show Server Status" button
- **Log Files**: Use "Open MCP Logs" for detailed logs

## Migration Complete! 🎉

Your BlendPro installation now supports:
- ✅ Claude Desktop compatible JSON configuration
- ✅ Multiple MCP server support
- ✅ Enhanced security validation
- ✅ Backward compatibility with legacy setups
- ✅ Auto-start capability
- ✅ Advanced UI with status indicators

## Future Enhancements

Completed in this version:
- ✅ Multiple server connections
- ✅ Advanced caching mechanisms
- ✅ Enhanced security features

Planned features:
- WebSocket transport support
- MCP prompt templates
- Batch resource operations
