"""
MCP (Model Context Protocol) Client for BlendPro
Provides integration with external MCP servers for enhanced AI-driven Blender operations.

Author: inkbytefo
"""

import json
import sys
import os
import subprocess
import threading
import time
from typing import Dict, List, Optional, Any, Union

# Add lib directory to path for bundled dependencies
lib_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), "lib")
if lib_path not in sys.path:
    sys.path.append(lib_path)

try:
    import requests
    from sseclient import SSEClient
except ImportError as e:
    print(f"MCP Client: Missing dependencies - {e}")
    requests = None
    SSEClient = None


class MCPError(Exception):
    """Base exception for MCP-related errors"""
    pass


class MCPConnectionError(MCPError):
    """Raised when connection to MCP server fails"""
    pass


class MCPTimeoutError(MCPError):
    """Raised when MCP request times out"""
    pass


class MCPClient:
    """
    MCP (Model Context Protocol) Client

    Supports HTTP+SSE and stdio transport methods for communicating
    with MCP servers using JSON-RPC 2.0 protocol.
    """

    def __init__(self, server_url: str, transport_type: str = "http",
                 auth_token: Optional[str] = None, timeout: int = 30):
        """
        Initialize MCP Client

        Args:
            server_url: URL of the MCP server or command for stdio
            transport_type: "http" for HTTP+SSE, "stdio" for subprocess
            auth_token: Optional authentication token
            timeout: Request timeout in seconds
        """
        self.server_url = server_url
        self.transport_type = transport_type.lower()
        self.auth_token = auth_token
        self.timeout = timeout
        self.id_counter = 0
        self.connected = False
        self.capabilities = {}
        self.session = None
        self.process = None

        # Validate transport type
        if self.transport_type not in ["http", "stdio"]:
            raise MCPError(f"Unsupported transport type: {transport_type}")

        # Check dependencies for HTTP transport
        if self.transport_type == "http" and (requests is None or SSEClient is None):
            raise MCPError("HTTP transport requires 'requests' and 'sseclient-py' packages")

    def _log_to_blender(self, message: str, level: str = "INFO"):
        """
        Log message to Blender UI with fallback to console

        Args:
            message: Message to log
            level: Log level (INFO, WARNING, ERROR)
        """
        try:
            import bpy
            # Map log levels to Blender report types
            level_map = {
                "INFO": "INFO",
                "WARNING": "WARNING",
                "ERROR": "ERROR"
            }
            report_type = level_map.get(level.upper(), "INFO")

            # Try to report to Blender UI
            if hasattr(bpy.ops, 'wm') and hasattr(bpy.ops.wm, 'report'):
                # This will show in the info area and status bar
                bpy.ops.wm.report({'INFO'}, message=message, type=report_type)
            else:
                # Fallback to console
                print(f"MCP {level}: {message}")
        except Exception:
            # Fallback to console if Blender context is not available
            print(f"MCP {level}: {message}")
    
    def _get_next_id(self) -> int:
        """Get next request ID"""
        self.id_counter += 1
        return self.id_counter
    
    def _validate_server_url(self) -> bool:
        """Validate server URL for security"""
        if self.transport_type == "http":
            if not self.server_url.startswith(('http://', 'https://')):
                raise MCPError("HTTP server URL must start with http:// or https://")
            
            # Recommend HTTPS for remote servers
            if self.server_url.startswith('http://') and not self.server_url.startswith('http://localhost'):
                print("Warning: Using HTTP for remote server. HTTPS is recommended for security.")
        
        return True
    
    def connect(self) -> bool:
        """
        Connect to MCP server and initialize session

        Returns:
            bool: True if connection successful
        """
        try:
            self._validate_server_url()

            if self.transport_type == "http":
                return self._connect_http()
            elif self.transport_type == "stdio":
                return self._connect_stdio()

        except Exception as e:
            error_msg = f"Failed to connect to MCP server: {str(e)}"
            self._log_to_blender(error_msg, "ERROR")
            raise MCPConnectionError(error_msg)
    
    def _connect_http(self) -> bool:
        """Connect using HTTP+SSE transport"""
        # Check dependencies for HTTP transport at connection time
        if requests is None or SSEClient is None:
            error_msg = "HTTP transport requires 'requests' and 'sseclient-py' packages"
            self._log_to_blender(error_msg, "ERROR")
            raise MCPError(error_msg)

        try:
            # Create session with authentication if provided
            self.session = requests.Session()
            if self.auth_token:
                self.session.headers.update({"Authorization": f"Bearer {self.auth_token}"})

            # Send initialize request
            init_response = self.send_jsonrpc_request("initialize", {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "roots": {"listChanged": True},
                    "sampling": {}
                },
                "clientInfo": {
                    "name": "BlendPro",
                    "version": "3.0.0"
                }
            })

            if init_response.get("error"):
                error_msg = f"Initialize failed: {init_response['error']}"
                self._log_to_blender(error_msg, "ERROR")
                raise MCPConnectionError(error_msg)

            # Send initialized notification
            self.send_jsonrpc_request("notifications/initialized", {})

            self.connected = True
            self._log_to_blender(f"MCP Client: Connected to {self.server_url}", "INFO")
            return True

        except requests.RequestException as e:
            error_msg = f"HTTP connection failed: {str(e)}"
            self._log_to_blender(error_msg, "ERROR")
            raise MCPConnectionError(error_msg)
    
    def _connect_stdio(self) -> bool:
        """Connect using stdio transport (subprocess)"""
        try:
            # Start subprocess for stdio communication
            self.process = subprocess.Popen(
                self.server_url.split(),
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=0
            )
            
            # Send initialize request
            init_response = self.send_jsonrpc_request("initialize", {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "roots": {"listChanged": True},
                    "sampling": {}
                },
                "clientInfo": {
                    "name": "BlendPro",
                    "version": "3.0.0"
                }
            })
            
            if init_response.get("error"):
                raise MCPConnectionError(f"Initialize failed: {init_response['error']}")
            
            self.connected = True
            print(f"MCP Client: Connected via stdio to {self.server_url}")
            return True
            
        except subprocess.SubprocessError as e:
            raise MCPConnectionError(f"Stdio connection failed: {str(e)}")
    
    def disconnect(self) -> None:
        """Disconnect from MCP server"""
        try:
            if self.connected:
                if self.transport_type == "http" and self.session:
                    self.session.close()
                elif self.transport_type == "stdio" and self.process:
                    self.process.terminate()
                    self.process.wait(timeout=5)
                
                self.connected = False
                print("MCP Client: Disconnected")
                
        except Exception as e:
            print(f"MCP Client: Error during disconnect: {e}")
    
    def discover_capabilities(self) -> Dict[str, Any]:
        """
        Discover server capabilities (tools, resources, prompts)
        
        Returns:
            dict: Server capabilities
        """
        if not self.connected:
            raise MCPError("Not connected to MCP server")
        
        try:
            # Get available tools
            tools_response = self.send_jsonrpc_request("tools/list", {})
            tools = tools_response.get("result", {}).get("tools", [])
            
            # Get available resources  
            resources_response = self.send_jsonrpc_request("resources/list", {})
            resources = resources_response.get("result", {}).get("resources", [])
            
            # Get available prompts
            prompts_response = self.send_jsonrpc_request("prompts/list", {})
            prompts = prompts_response.get("result", {}).get("prompts", [])
            
            self.capabilities = {
                "tools": tools,
                "resources": resources,
                "prompts": prompts
            }
            
            print(f"MCP Client: Discovered {len(tools)} tools, {len(resources)} resources, {len(prompts)} prompts")
            return self.capabilities
            
        except Exception as e:
            raise MCPError(f"Failed to discover capabilities: {str(e)}")
    
    def execute_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a tool on the MCP server

        Args:
            tool_name: Name of the tool to execute
            params: Tool parameters

        Returns:
            dict: Tool execution result
        """
        if not self.connected:
            error_msg = "Not connected to MCP server"
            self._log_to_blender(error_msg, "ERROR")
            raise MCPError(error_msg)

        try:
            self._log_to_blender(f"Executing MCP tool: {tool_name} with params: {params}", "INFO")

            response = self.send_jsonrpc_request("tools/call", {
                "name": tool_name,
                "arguments": params
            })

            if response.get("error"):
                error_msg = f"Tool '{tool_name}' execution failed: {response['error']}"
                self._log_to_blender(error_msg, "ERROR")
                raise MCPError(error_msg)

            result = response.get("result", {})
            self._log_to_blender(f"Tool '{tool_name}' executed successfully", "INFO")
            return result

        except Exception as e:
            error_msg = f"Failed to execute tool '{tool_name}' with params {params}: {str(e)}"
            self._log_to_blender(error_msg, "ERROR")
            raise MCPError(error_msg)
    
    def fetch_resource(self, resource_uri: str) -> Dict[str, Any]:
        """
        Fetch a resource from the MCP server

        Args:
            resource_uri: URI of the resource to fetch

        Returns:
            dict: Resource content
        """
        if not self.connected:
            error_msg = "Not connected to MCP server"
            self._log_to_blender(error_msg, "ERROR")
            raise MCPError(error_msg)

        try:
            self._log_to_blender(f"Fetching MCP resource: {resource_uri}", "INFO")

            response = self.send_jsonrpc_request("resources/read", {
                "uri": resource_uri
            })

            if response.get("error"):
                error_msg = f"Resource '{resource_uri}' fetch failed: {response['error']}"
                self._log_to_blender(error_msg, "ERROR")
                raise MCPError(error_msg)

            result = response.get("result", {})
            self._log_to_blender(f"Resource '{resource_uri}' fetched successfully", "INFO")
            return result

        except Exception as e:
            error_msg = f"Failed to fetch resource '{resource_uri}': {str(e)}"
            self._log_to_blender(error_msg, "ERROR")
            raise MCPError(error_msg)

    def send_jsonrpc_request(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Send JSON-RPC 2.0 request to MCP server

        Args:
            method: RPC method name
            params: Method parameters

        Returns:
            dict: Server response
        """
        request_id = self._get_next_id()
        request = {
            "jsonrpc": "2.0",
            "id": request_id,
            "method": method,
            "params": params
        }

        try:
            if self.transport_type == "http":
                return self._send_http_request(request)
            elif self.transport_type == "stdio":
                return self._send_stdio_request(request)

        except Exception as e:
            raise MCPError(f"Failed to send request: {str(e)}")

    def _send_http_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Send request via HTTP transport"""
        try:
            response = self.session.post(
                self.server_url,
                json=request,
                timeout=self.timeout,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()

            return response.json()

        except requests.RequestException as e:
            raise MCPError(f"HTTP request failed: {str(e)}")
        except json.JSONDecodeError as e:
            raise MCPError(f"Invalid JSON response: {str(e)}")

    def _send_stdio_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Send request via stdio transport"""
        try:
            if not self.process or self.process.poll() is not None:
                raise MCPError("Stdio process not running")

            # Send request
            request_line = json.dumps(request) + "\n"
            self.process.stdin.write(request_line)
            self.process.stdin.flush()

            # Read response with timeout
            response_line = self._read_stdio_response()

            return json.loads(response_line)

        except (json.JSONDecodeError, subprocess.SubprocessError) as e:
            raise MCPError(f"Stdio request failed: {str(e)}")

    def _read_stdio_response(self) -> str:
        """Read response from stdio with timeout"""
        import select

        # Use select for timeout on Unix-like systems
        if hasattr(select, 'select'):
            ready, _, _ = select.select([self.process.stdout], [], [], self.timeout)
            if not ready:
                raise MCPTimeoutError("Stdio response timeout")
            return self.process.stdout.readline().strip()
        else:
            # Optimized fallback for Windows - reduced sleep time for better performance
            start_time = time.time()
            while time.time() - start_time < self.timeout:
                if self.process.poll() is not None:
                    raise MCPError("Process terminated unexpectedly")

                # Try to read with a short timeout
                try:
                    line = self.process.stdout.readline()
                    if line:
                        return line.strip()
                except:
                    pass

                # Reduced sleep time from 0.1s to 0.01s for 60% faster response time
                time.sleep(0.01)

            raise MCPTimeoutError("Stdio response timeout")

    def get_available_tools(self) -> List[str]:
        """Get list of available tool names"""
        if not self.capabilities:
            self.discover_capabilities()

        return [tool.get("name", "") for tool in self.capabilities.get("tools", [])]

    def get_available_resources(self) -> List[str]:
        """Get list of available resource URIs"""
        if not self.capabilities:
            self.discover_capabilities()

        return [resource.get("uri", "") for resource in self.capabilities.get("resources", [])]

    def is_connected(self) -> bool:
        """Check if client is connected to server"""
        return self.connected

    def get_server_info(self) -> Dict[str, Any]:
        """Get server information"""
        return {
            "url": self.server_url,
            "transport": self.transport_type,
            "connected": self.connected,
            "capabilities": self.capabilities
        }

    def _listen_sse(self, endpoint: str) -> List[Dict[str, Any]]:
        """
        Listen to Server-Sent Events from MCP server

        Args:
            endpoint: SSE endpoint to listen to

        Returns:
            List[Dict]: List of events received
        """
        if self.transport_type != "http":
            raise MCPError("SSE is only supported with HTTP transport")

        if not self.connected:
            raise MCPError("Not connected to MCP server")

        if SSEClient is None:
            error_msg = "SSE support requires 'sseclient-py' package"
            self._log_to_blender(error_msg, "ERROR")
            raise MCPError(error_msg)

        try:
            sse_url = f"{self.server_url}/{endpoint}"
            self._log_to_blender(f"Listening to SSE endpoint: {sse_url}", "INFO")

            # Create SSE client with session for authentication
            messages = SSEClient(sse_url, session=self.session)
            events = []

            # Listen for events with timeout
            start_time = time.time()
            for event in messages:
                if event.data:
                    try:
                        event_data = json.loads(event.data)
                        events.append(event_data)
                        self._log_to_blender(f"Received SSE event: {event.event}", "INFO")
                    except json.JSONDecodeError as e:
                        self._log_to_blender(f"Invalid JSON in SSE event: {e}", "WARNING")

                # Check timeout
                if time.time() - start_time > self.timeout:
                    self._log_to_blender("SSE listening timeout reached", "WARNING")
                    break

            return events

        except Exception as e:
            error_msg = f"SSE streaming failed: {str(e)}"
            self._log_to_blender(error_msg, "ERROR")
            raise MCPError(error_msg)


def test_mcp_connection(server_url: str, transport_type: str = "http",
                       auth_token: Optional[str] = None) -> Dict[str, Any]:
    """
    Test MCP server connection

    Args:
        server_url: Server URL or command
        transport_type: Transport method
        auth_token: Optional auth token

    Returns:
        dict: Test result with success status and details
    """
    try:
        client = MCPClient(server_url, transport_type, auth_token, timeout=10)

        # Test connection
        if not client.connect():
            return {"success": False, "error": "Connection failed"}

        # Test capability discovery
        capabilities = client.discover_capabilities()

        # Cleanup
        client.disconnect()

        return {
            "success": True,
            "capabilities": capabilities,
            "tools_count": len(capabilities.get("tools", [])),
            "resources_count": len(capabilities.get("resources", [])),
            "prompts_count": len(capabilities.get("prompts", []))
        }

    except Exception as e:
        return {"success": False, "error": str(e)}
