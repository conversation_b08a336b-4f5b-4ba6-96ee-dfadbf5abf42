"""
MCP Configuration Parser for BlendPro
Supports Claude Desktop compatible JSON configuration format.

Author: inkbytefo
"""

import json
import os
import sys
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from pathlib import Path

# Add lib directory to path for bundled dependencies
lib_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), "lib")
if lib_path not in sys.path:
    sys.path.append(lib_path)


@dataclass
class MCPServerConfig:
    """Configuration for a single MCP server"""
    name: str
    command: str
    args: List[str] = field(default_factory=list)
    env: Dict[str, str] = field(default_factory=dict)
    cwd: Optional[str] = None
    enabled: bool = True
    timeout: int = 30
    transport_type: str = "stdio"  # stdio or http
    
    def __post_init__(self):
        """Validate configuration after initialization"""
        if not self.name:
            raise ValueError("Server name cannot be empty")
        if not self.command:
            raise ValueError("Server command cannot be empty")
        if self.timeout <= 0:
            raise ValueError("Timeout must be positive")
        if self.transport_type not in ["stdio", "http"]:
            raise ValueError("Transport type must be 'stdio' or 'http'")


@dataclass 
class LegacyMCPConfig:
    """Legacy single-server MCP configuration"""
    enabled: bool = False
    server_url: str = ""
    transport_type: str = "http"
    auth_token: Optional[str] = None
    timeout: int = 30


@dataclass
class MCPConfiguration:
    """Complete MCP configuration"""
    servers: Dict[str, MCPServerConfig] = field(default_factory=dict)
    legacy: LegacyMCPConfig = field(default_factory=LegacyMCPConfig)
    config_version: str = "1.0"
    
    def get_enabled_servers(self) -> Dict[str, MCPServerConfig]:
        """Get only enabled servers"""
        return {name: config for name, config in self.servers.items() if config.enabled}
    
    def get_server_names(self) -> List[str]:
        """Get list of all server names"""
        return list(self.servers.keys())
    
    def add_server(self, server_config: MCPServerConfig) -> None:
        """Add a new server configuration"""
        self.servers[server_config.name] = server_config
    
    def remove_server(self, name: str) -> bool:
        """Remove a server configuration"""
        if name in self.servers:
            del self.servers[name]
            return True
        return False


class MCPConfigParser:
    """Parser for MCP configuration files"""
    
    SUPPORTED_COMMANDS = ["npx", "python", "python3", "node", "pip", "pipx"]
    
    def __init__(self):
        self.config = MCPConfiguration()
    
    def load_from_file(self, file_path: str) -> MCPConfiguration:
        """
        Load configuration from JSON file
        
        Args:
            file_path: Path to JSON configuration file
            
        Returns:
            MCPConfiguration: Parsed configuration
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Configuration file not found: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return self.parse_json(data)
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in configuration file: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to load configuration: {e}")
    
    def parse_json(self, data: Dict[str, Any]) -> MCPConfiguration:
        """
        Parse JSON data into MCPConfiguration
        
        Args:
            data: JSON data dictionary
            
        Returns:
            MCPConfiguration: Parsed configuration
        """
        config = MCPConfiguration()
        
        # Parse mcpServers section (Claude Desktop format)
        if "mcpServers" in data:
            servers_data = data["mcpServers"]
            if not isinstance(servers_data, dict):
                raise ValueError("mcpServers must be a dictionary")
            
            for server_name, server_config in servers_data.items():
                parsed_server = self._parse_server_config(server_name, server_config)
                config.add_server(parsed_server)
        
        # Parse legacy configuration
        if "legacy" in data:
            legacy_data = data["legacy"]
            config.legacy = self._parse_legacy_config(legacy_data)
        
        # Parse metadata
        config.config_version = data.get("config_version", "1.0")
        
        return config
    
    def _parse_server_config(self, name: str, data: Dict[str, Any]) -> MCPServerConfig:
        """Parse individual server configuration"""
        if not isinstance(data, dict):
            raise ValueError(f"Server '{name}' configuration must be a dictionary")
        
        # Required fields
        command = data.get("command", "")
        if not command:
            raise ValueError(f"Server '{name}' must have a command")
        
        # Validate command
        if command not in self.SUPPORTED_COMMANDS:
            print(f"Warning: Command '{command}' for server '{name}' is not in supported list")
        
        # Parse arguments
        args = data.get("args", [])
        if not isinstance(args, list):
            raise ValueError(f"Server '{name}' args must be a list")
        
        # Parse environment variables
        env = data.get("env", {})
        if not isinstance(env, dict):
            raise ValueError(f"Server '{name}' env must be a dictionary")
        
        # Parse optional fields
        cwd = data.get("cwd")
        enabled = data.get("enabled", True)
        timeout = data.get("timeout", 30)
        transport_type = data.get("transport_type", "stdio")
        
        return MCPServerConfig(
            name=name,
            command=command,
            args=args,
            env=env,
            cwd=cwd,
            enabled=enabled,
            timeout=timeout,
            transport_type=transport_type
        )
    
    def _parse_legacy_config(self, data: Dict[str, Any]) -> LegacyMCPConfig:
        """Parse legacy configuration section"""
        return LegacyMCPConfig(
            enabled=data.get("enabled", False),
            server_url=data.get("server_url", ""),
            transport_type=data.get("transport_type", "http"),
            auth_token=data.get("auth_token"),
            timeout=data.get("timeout", 30)
        )
    
    def save_to_file(self, config: MCPConfiguration, file_path: str) -> None:
        """
        Save configuration to JSON file
        
        Args:
            config: Configuration to save
            file_path: Output file path
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # Convert to JSON format
            data = self.to_json(config)
            
            # Write to file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            raise RuntimeError(f"Failed to save configuration: {e}")
    
    def to_json(self, config: MCPConfiguration) -> Dict[str, Any]:
        """Convert MCPConfiguration to JSON-serializable dictionary"""
        data = {
            "config_version": config.config_version,
            "mcpServers": {},
            "legacy": {
                "enabled": config.legacy.enabled,
                "server_url": config.legacy.server_url,
                "transport_type": config.legacy.transport_type,
                "timeout": config.legacy.timeout
            }
        }
        
        # Add auth token only if present
        if config.legacy.auth_token:
            data["legacy"]["auth_token"] = config.legacy.auth_token
        
        # Convert servers
        for name, server in config.servers.items():
            server_data = {
                "command": server.command,
                "args": server.args,
                "enabled": server.enabled,
                "timeout": server.timeout,
                "transport_type": server.transport_type
            }
            
            # Add optional fields only if present
            if server.env:
                server_data["env"] = server.env
            if server.cwd:
                server_data["cwd"] = server.cwd
            
            data["mcpServers"][name] = server_data
        
        return data
    
    def validate_config(self, config: MCPConfiguration) -> List[str]:
        """
        Validate configuration and return list of warnings/errors
        
        Args:
            config: Configuration to validate
            
        Returns:
            List[str]: List of validation messages
        """
        warnings = []
        
        # Check if any servers are configured
        if not config.servers and not config.legacy.enabled:
            warnings.append("No MCP servers configured")
        
        # Validate each server
        for name, server in config.servers.items():
            try:
                # Check command availability (basic check)
                if server.command not in self.SUPPORTED_COMMANDS:
                    warnings.append(f"Server '{name}': Command '{server.command}' may not be available")
                
                # Check working directory
                if server.cwd and not os.path.exists(server.cwd):
                    warnings.append(f"Server '{name}': Working directory '{server.cwd}' does not exist")
                
                # Check for required environment variables
                if server.command == "npx" and not server.args:
                    warnings.append(f"Server '{name}': npx command requires package name in args")
                
            except Exception as e:
                warnings.append(f"Server '{name}': Validation error - {e}")
        
        return warnings
    
    def create_example_config(self) -> MCPConfiguration:
        """Create an example configuration with common servers"""
        config = MCPConfiguration()
        
        # Filesystem server
        filesystem_server = MCPServerConfig(
            name="filesystem",
            command="npx",
            args=["-y", "@modelcontextprotocol/server-filesystem", "/path/to/allowed/files"],
            enabled=True
        )
        config.add_server(filesystem_server)
        
        # Memory server
        memory_server = MCPServerConfig(
            name="memory",
            command="npx",
            args=["-y", "@modelcontextprotocol/server-memory"],
            enabled=True
        )
        config.add_server(memory_server)
        
        # GitHub server (with environment variable)
        github_server = MCPServerConfig(
            name="github",
            command="npx",
            args=["-y", "@modelcontextprotocol/server-github"],
            env={"GITHUB_PERSONAL_ACCESS_TOKEN": "<YOUR_TOKEN>"},
            enabled=False  # Disabled by default since it needs token
        )
        config.add_server(github_server)
        
        return config


def get_default_config_path() -> str:
    """Get default configuration file path"""
    try:
        import bpy
        # Use Blender's user config directory
        config_dir = bpy.utils.user_resource('CONFIG')
        return os.path.join(config_dir, "blendpro_mcp_config.json")
    except ImportError:
        # Fallback for testing outside Blender
        home_dir = Path.home()
        return str(home_dir / ".blendpro_mcp_config.json")


# Example usage and testing
if __name__ == "__main__":
    parser = MCPConfigParser()
    
    # Create example configuration
    example_config = parser.create_example_config()
    
    # Save example
    example_path = "example_mcp_config.json"
    parser.save_to_file(example_config, example_path)
    print(f"Example configuration saved to: {example_path}")
    
    # Load and validate
    loaded_config = parser.load_from_file(example_path)
    warnings = parser.validate_config(loaded_config)
    
    if warnings:
        print("Validation warnings:")
        for warning in warnings:
            print(f"  - {warning}")
    else:
        print("Configuration is valid!")
