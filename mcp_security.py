"""
MCP Security and Validation Module for BlendPro
Provides security checks and validation for MCP server configurations.

Author: inkbytefo
"""

import os
import sys
import re
import subprocess
from typing import Dict, List, Optional, Set, Tuple
from pathlib import Path

# Add lib directory to path for bundled dependencies
lib_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), "lib")
if lib_path not in sys.path:
    sys.path.append(lib_path)

from mcp_config_parser import MCPServerConfig, MCPConfiguration


class MCPSecurityError(Exception):
    """Raised when security validation fails"""
    pass


class MCPSecurityValidator:
    """Security validator for MCP configurations"""
    
    # Allowed commands for MCP servers
    ALLOWED_COMMANDS = {
        "npx", "npm", "node", "python", "python3", "pip", "pipx", 
        "java", "mvn", "gradle", "dotnet", "go", "cargo", "ruby", "gem"
    }
    
    # Dangerous commands that should never be allowed
    DANGEROUS_COMMANDS = {
        "rm", "del", "format", "fdisk", "dd", "mkfs", "shutdown", "reboot",
        "sudo", "su", "chmod", "chown", "passwd", "useradd", "userdel",
        "systemctl", "service", "kill", "killall", "pkill", "taskkill"
    }
    
    # Dangerous environment variables
    DANGEROUS_ENV_VARS = {
        "PATH", "LD_LIBRARY_PATH", "PYTHONPATH", "CLASSPATH", "HOME", "USER"
    }
    
    # Maximum allowed argument length
    MAX_ARG_LENGTH = 1000
    
    # Maximum number of arguments
    MAX_ARGS_COUNT = 50
    
    def __init__(self, strict_mode: bool = True):
        """
        Initialize security validator
        
        Args:
            strict_mode: If True, applies stricter security checks
        """
        self.strict_mode = strict_mode
        self.warnings: List[str] = []
        self.errors: List[str] = []
    
    def validate_configuration(self, config: MCPConfiguration) -> Tuple[bool, List[str], List[str]]:
        """
        Validate entire MCP configuration
        
        Args:
            config: Configuration to validate
            
        Returns:
            Tuple of (is_valid, warnings, errors)
        """
        self.warnings.clear()
        self.errors.clear()
        
        # Validate each server
        for name, server_config in config.servers.items():
            try:
                self.validate_server_config(server_config)
            except MCPSecurityError as e:
                self.errors.append(f"Server '{name}': {str(e)}")
        
        # Check for duplicate server names
        if len(config.servers) != len(set(config.servers.keys())):
            self.errors.append("Duplicate server names found")
        
        # Validate legacy configuration if enabled
        if config.legacy.enabled:
            self.validate_legacy_config(config.legacy)
        
        is_valid = len(self.errors) == 0
        return is_valid, self.warnings.copy(), self.errors.copy()
    
    def validate_server_config(self, server_config: MCPServerConfig) -> None:
        """
        Validate individual server configuration
        
        Args:
            server_config: Server configuration to validate
            
        Raises:
            MCPSecurityError: If validation fails
        """
        # Validate command
        self._validate_command(server_config.command)
        
        # Validate arguments
        self._validate_arguments(server_config.args)
        
        # Validate environment variables
        self._validate_environment(server_config.env)
        
        # Validate working directory
        if server_config.cwd:
            self._validate_working_directory(server_config.cwd)
        
        # Validate timeout
        if server_config.timeout <= 0 or server_config.timeout > 300:
            raise MCPSecurityError(f"Invalid timeout: {server_config.timeout}. Must be between 1-300 seconds")
    
    def validate_legacy_config(self, legacy_config) -> None:
        """Validate legacy configuration"""
        if legacy_config.server_url:
            self._validate_server_url(legacy_config.server_url)
        
        if legacy_config.timeout <= 0 or legacy_config.timeout > 300:
            self.warnings.append(f"Legacy timeout {legacy_config.timeout} is outside recommended range (1-300)")
    
    def _validate_command(self, command: str) -> None:
        """Validate command security"""
        if not command:
            raise MCPSecurityError("Command cannot be empty")
        
        # Check for dangerous commands
        if command.lower() in self.DANGEROUS_COMMANDS:
            raise MCPSecurityError(f"Dangerous command not allowed: {command}")
        
        # Check if command is in allowed list (strict mode)
        if self.strict_mode and command not in self.ALLOWED_COMMANDS:
            self.warnings.append(f"Command '{command}' is not in the allowed list")
        
        # Check for path traversal attempts
        if ".." in command or "/" in command or "\\" in command:
            if not self._is_safe_path(command):
                raise MCPSecurityError(f"Potentially unsafe command path: {command}")
        
        # Check for shell injection attempts
        dangerous_chars = [";", "&", "|", "`", "$", "(", ")", "<", ">"]
        if any(char in command for char in dangerous_chars):
            raise MCPSecurityError(f"Command contains potentially dangerous characters: {command}")
    
    def _validate_arguments(self, args: List[str]) -> None:
        """Validate command arguments"""
        if len(args) > self.MAX_ARGS_COUNT:
            raise MCPSecurityError(f"Too many arguments: {len(args)} > {self.MAX_ARGS_COUNT}")
        
        for i, arg in enumerate(args):
            if len(arg) > self.MAX_ARG_LENGTH:
                raise MCPSecurityError(f"Argument {i} too long: {len(arg)} > {self.MAX_ARG_LENGTH}")
            
            # Check for dangerous patterns
            if ".." in arg and ("/" in arg or "\\" in arg):
                if not self._is_safe_path(arg):
                    raise MCPSecurityError(f"Potentially unsafe path in argument {i}: {arg}")
            
            # Check for shell injection in arguments
            dangerous_chars = [";", "&", "|", "`", "$"]
            if any(char in arg for char in dangerous_chars):
                self.warnings.append(f"Argument {i} contains potentially dangerous characters: {arg}")
    
    def _validate_environment(self, env: Dict[str, str]) -> None:
        """Validate environment variables"""
        for key, value in env.items():
            # Check for dangerous environment variables
            if key in self.DANGEROUS_ENV_VARS:
                self.warnings.append(f"Modifying system environment variable '{key}' can be dangerous")
            
            # Validate key format
            if not re.match(r'^[A-Z_][A-Z0-9_]*$', key):
                self.warnings.append(f"Environment variable name '{key}' doesn't follow conventions")
            
            # Check value length
            if len(value) > 10000:
                raise MCPSecurityError(f"Environment variable '{key}' value too long")
            
            # Check for dangerous patterns in values
            if ".." in value and ("/" in value or "\\" in value):
                if not self._is_safe_path(value):
                    self.warnings.append(f"Environment variable '{key}' contains potentially unsafe path")
    
    def _validate_working_directory(self, cwd: str) -> None:
        """Validate working directory"""
        if not cwd:
            return
        
        # Check for path traversal
        if ".." in cwd:
            raise MCPSecurityError(f"Working directory contains path traversal: {cwd}")
        
        # Check if path exists (warning only)
        if not os.path.exists(cwd):
            self.warnings.append(f"Working directory does not exist: {cwd}")
        
        # Check if it's a reasonable path
        try:
            path = Path(cwd).resolve()
            # Warn if it's a system directory
            system_dirs = ["/bin", "/sbin", "/etc", "/sys", "/proc", "C:\\Windows", "C:\\System32"]
            if any(str(path).startswith(sys_dir) for sys_dir in system_dirs):
                self.warnings.append(f"Working directory is in system location: {cwd}")
        except Exception:
            self.warnings.append(f"Invalid working directory path: {cwd}")
    
    def _validate_server_url(self, url: str) -> None:
        """Validate server URL for legacy configuration"""
        if not url:
            return
        
        # Basic URL validation
        if not (url.startswith("http://") or url.startswith("https://") or url.startswith("stdio")):
            raise MCPSecurityError(f"Invalid server URL format: {url}")
        
        # Warn about HTTP (not HTTPS)
        if url.startswith("http://") and not url.startswith("http://localhost"):
            self.warnings.append("Using HTTP instead of HTTPS for remote server")
        
        # Check for suspicious URLs
        suspicious_patterns = ["javascript:", "file:", "ftp:", "data:"]
        if any(pattern in url.lower() for pattern in suspicious_patterns):
            raise MCPSecurityError(f"Suspicious URL scheme detected: {url}")
    
    def _is_safe_path(self, path: str) -> bool:
        """Check if a path is safe (doesn't escape intended directory)"""
        try:
            # Resolve the path and check if it's within reasonable bounds
            resolved = Path(path).resolve()
            
            # Check if it tries to access system directories
            system_dirs = ["/bin", "/sbin", "/etc", "/sys", "/proc", "/root", 
                          "C:\\Windows", "C:\\System32", "C:\\Program Files"]
            
            for sys_dir in system_dirs:
                if str(resolved).startswith(sys_dir):
                    return False
            
            return True
        except Exception:
            return False
    
    def sanitize_environment(self, env: Dict[str, str]) -> Dict[str, str]:
        """
        Sanitize environment variables
        
        Args:
            env: Original environment variables
            
        Returns:
            Sanitized environment variables
        """
        sanitized = {}
        
        for key, value in env.items():
            # Skip dangerous environment variables
            if key in self.DANGEROUS_ENV_VARS:
                self.warnings.append(f"Skipping dangerous environment variable: {key}")
                continue
            
            # Sanitize key (only allow alphanumeric and underscore)
            clean_key = re.sub(r'[^A-Z0-9_]', '', key.upper())
            if clean_key != key:
                self.warnings.append(f"Environment variable key sanitized: {key} -> {clean_key}")
            
            # Sanitize value (remove null bytes and control characters)
            clean_value = re.sub(r'[\x00-\x1f\x7f]', '', value)
            if clean_value != value:
                self.warnings.append(f"Environment variable value sanitized: {key}")
            
            # Limit value length
            if len(clean_value) > 1000:
                clean_value = clean_value[:1000]
                self.warnings.append(f"Environment variable value truncated: {key}")
            
            sanitized[clean_key] = clean_value
        
        return sanitized
    
    def get_command_whitelist(self) -> Set[str]:
        """Get the current command whitelist"""
        return self.ALLOWED_COMMANDS.copy()
    
    def add_allowed_command(self, command: str) -> None:
        """Add a command to the allowed list"""
        self.ALLOWED_COMMANDS.add(command)
    
    def remove_allowed_command(self, command: str) -> None:
        """Remove a command from the allowed list"""
        self.ALLOWED_COMMANDS.discard(command)


def validate_mcp_config_file(config_path: str, strict_mode: bool = True) -> Tuple[bool, List[str], List[str]]:
    """
    Validate MCP configuration file
    
    Args:
        config_path: Path to configuration file
        strict_mode: Enable strict validation
        
    Returns:
        Tuple of (is_valid, warnings, errors)
    """
    try:
        from mcp_config_parser import MCPConfigParser
        
        parser = MCPConfigParser()
        config = parser.load_from_file(config_path)
        
        validator = MCPSecurityValidator(strict_mode)
        return validator.validate_configuration(config)
        
    except Exception as e:
        return False, [], [f"Failed to validate configuration: {str(e)}"]


# Example usage and testing
if __name__ == "__main__":
    # Test security validator
    validator = MCPSecurityValidator(strict_mode=True)
    
    # Test dangerous command
    try:
        from mcp_config_parser import MCPServerConfig
        
        dangerous_config = MCPServerConfig(
            name="test",
            command="rm",  # Dangerous command
            args=["-rf", "/"]
        )
        validator.validate_server_config(dangerous_config)
        print("❌ Should have failed with dangerous command")
    except MCPSecurityError as e:
        print(f"✅ Correctly caught dangerous command: {e}")
    
    # Test safe command
    try:
        safe_config = MCPServerConfig(
            name="test",
            command="npx",
            args=["-y", "@modelcontextprotocol/server-filesystem", "/safe/path"]
        )
        validator.validate_server_config(safe_config)
        print("✅ Safe command validated successfully")
    except MCPSecurityError as e:
        print(f"❌ Safe command failed validation: {e}")
    
    print(f"Warnings: {validator.warnings}")
    print(f"Errors: {validator.errors}")
