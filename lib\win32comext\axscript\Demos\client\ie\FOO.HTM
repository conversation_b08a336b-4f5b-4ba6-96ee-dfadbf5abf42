<HTML>
<BODY>
Let's try this out:

<FORM>
<INPUT NAME="Button1" TYPE="Button" OnClick="foo1" LANGUAGE="VBScript">
<FORM METHOD="GET" NAME="MyForm">
<INPUT TYPE="TEXT" NAME="Text1" SIZE=25>
<INPUT TYPE="SUBMIT">
<INPUT NAME="Button2" TYPE="Button" VALUE="Hi" OnClick="text2.value=window.document.location" LANGUAGE="VBScript">
<INPUT TYPE="TEXT" SIZE=25 NAME="Text2">
</FORM>
And here is a second form
<P>
<FORM NAME="Form2" METHOD="GET">
<INPUT NAME="Button2" TYPE="Button" VALUE="Hi" OnClick="button2.value=window.document.location" LANGUAGE="VBScript">
</FORM><BR>

<SCRIPT LANGUAGE="JScript">
y=15
</SCRIPT>

<SCRIPT LANGUAGE="VBScript">
x = 13
Sub foo1
   Dim y
   y = 14
   alert "Hello"
End Sub

Sub Window_OnLoad
   foo1
   MyForm.button2.value = "Loaded"
   Form2.button2.value = "Loaded"
End Sub
Sub Link_MouseMove(b, s, x, y)
   MsgBox b
End Sub
</SCRIPT>

<SCRIPT LANGUAGE="Python">
print("Python loaded")
</SCRIPT>

</BODY>
</HTML>
