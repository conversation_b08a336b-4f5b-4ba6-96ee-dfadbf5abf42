"""
MCP Process Manager for BlendPro
Manages lifecycle of command-based MCP servers (npx, python, node, etc.)

Author: inkbytefo
"""

import os
import sys
import subprocess
import threading
import time
import signal
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum

# Add lib directory to path for bundled dependencies
lib_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), "lib")
if lib_path not in sys.path:
    sys.path.append(lib_path)

from mcp_config_parser import MCPServerConfig


class ProcessStatus(Enum):
    """Process status enumeration"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"
    CRASHED = "crashed"


@dataclass
class ProcessInfo:
    """Information about a managed process"""
    name: str
    config: MCPServerConfig
    process: Optional[subprocess.Popen] = None
    status: ProcessStatus = ProcessStatus.STOPPED
    pid: Optional[int] = None
    start_time: Optional[float] = None
    error_message: Optional[str] = None
    restart_count: int = 0
    last_output: str = ""
    
    @property
    def uptime(self) -> float:
        """Get process uptime in seconds"""
        if self.start_time and self.status == ProcessStatus.RUNNING:
            return time.time() - self.start_time
        return 0.0
    
    @property
    def is_alive(self) -> bool:
        """Check if process is alive"""
        return (self.process is not None and 
                self.process.poll() is None and 
                self.status == ProcessStatus.RUNNING)


class MCPProcessManager:
    """Manager for MCP server processes"""
    
    def __init__(self, log_callback: Optional[Callable[[str, str], None]] = None):
        """
        Initialize process manager
        
        Args:
            log_callback: Optional callback for logging (level, message)
        """
        self.processes: Dict[str, ProcessInfo] = {}
        self.log_callback = log_callback
        self._monitor_thread = None
        self._monitor_running = False
        self._lock = threading.Lock()
        
        # Start monitoring thread
        self._start_monitoring()
    
    def _log(self, level: str, message: str) -> None:
        """Log message using callback or print"""
        if self.log_callback:
            self.log_callback(level, message)
        else:
            print(f"MCP Process Manager [{level}]: {message}")
    
    def start_server(self, config: MCPServerConfig) -> bool:
        """
        Start an MCP server process
        
        Args:
            config: Server configuration
            
        Returns:
            bool: True if started successfully
        """
        with self._lock:
            if config.name in self.processes:
                process_info = self.processes[config.name]
                if process_info.is_alive:
                    self._log("WARNING", f"Server '{config.name}' is already running")
                    return True
            
            try:
                self._log("INFO", f"Starting MCP server: {config.name}")
                
                # Prepare command
                command = self._prepare_command(config)
                
                # Prepare environment
                env = self._prepare_environment(config)
                
                # Prepare working directory
                cwd = config.cwd if config.cwd else None
                
                # Create process info
                process_info = ProcessInfo(
                    name=config.name,
                    config=config,
                    status=ProcessStatus.STARTING
                )
                
                # Start process
                process = subprocess.Popen(
                    command,
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=0,
                    env=env,
                    cwd=cwd,
                    # Platform-specific process group handling
                    **self._get_platform_process_args()
                )
                
                # Update process info
                process_info.process = process
                process_info.pid = process.pid
                process_info.start_time = time.time()
                process_info.status = ProcessStatus.RUNNING
                
                self.processes[config.name] = process_info
                
                self._log("INFO", f"Server '{config.name}' started with PID {process.pid}")
                return True
                
            except Exception as e:
                error_msg = f"Failed to start server '{config.name}': {e}"
                self._log("ERROR", error_msg)
                
                if config.name in self.processes:
                    self.processes[config.name].status = ProcessStatus.ERROR
                    self.processes[config.name].error_message = str(e)
                
                return False
    
    def stop_server(self, name: str, timeout: int = 10) -> bool:
        """
        Stop an MCP server process
        
        Args:
            name: Server name
            timeout: Timeout in seconds for graceful shutdown
            
        Returns:
            bool: True if stopped successfully
        """
        with self._lock:
            if name not in self.processes:
                self._log("WARNING", f"Server '{name}' not found")
                return False
            
            process_info = self.processes[name]
            
            if not process_info.is_alive:
                self._log("INFO", f"Server '{name}' is already stopped")
                process_info.status = ProcessStatus.STOPPED
                return True
            
            try:
                self._log("INFO", f"Stopping server '{name}'")
                process_info.status = ProcessStatus.STOPPING
                
                # Try graceful shutdown first
                process_info.process.terminate()
                
                # Wait for graceful shutdown
                try:
                    process_info.process.wait(timeout=timeout)
                    self._log("INFO", f"Server '{name}' stopped gracefully")
                except subprocess.TimeoutExpired:
                    # Force kill if graceful shutdown failed
                    self._log("WARNING", f"Server '{name}' did not stop gracefully, force killing")
                    process_info.process.kill()
                    process_info.process.wait()
                
                process_info.status = ProcessStatus.STOPPED
                return True
                
            except Exception as e:
                error_msg = f"Failed to stop server '{name}': {e}"
                self._log("ERROR", error_msg)
                process_info.status = ProcessStatus.ERROR
                process_info.error_message = str(e)
                return False
    
    def restart_server(self, name: str) -> bool:
        """
        Restart an MCP server
        
        Args:
            name: Server name
            
        Returns:
            bool: True if restarted successfully
        """
        if name not in self.processes:
            self._log("ERROR", f"Server '{name}' not found")
            return False
        
        config = self.processes[name].config
        
        # Stop first
        if not self.stop_server(name):
            return False
        
        # Wait a moment
        time.sleep(1)
        
        # Start again
        success = self.start_server(config)
        
        if success:
            self.processes[name].restart_count += 1
            self._log("INFO", f"Server '{name}' restarted (count: {self.processes[name].restart_count})")
        
        return success
    
    def get_server_status(self, name: str) -> Optional[ProcessInfo]:
        """Get server status information"""
        return self.processes.get(name)
    
    def list_servers(self) -> Dict[str, ProcessInfo]:
        """Get all server information"""
        return self.processes.copy()
    
    def stop_all_servers(self) -> None:
        """Stop all running servers"""
        self._log("INFO", "Stopping all MCP servers")
        
        server_names = list(self.processes.keys())
        for name in server_names:
            self.stop_server(name)
    
    def cleanup(self) -> None:
        """Cleanup resources and stop monitoring"""
        self._monitor_running = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        
        self.stop_all_servers()
    
    def _prepare_command(self, config: MCPServerConfig) -> List[str]:
        """Prepare command list for subprocess"""
        command = [config.command] + config.args
        
        # Validate command exists
        if not self._command_exists(config.command):
            raise RuntimeError(f"Command '{config.command}' not found in PATH")
        
        return command
    
    def _prepare_environment(self, config: MCPServerConfig) -> Dict[str, str]:
        """Prepare environment variables"""
        env = os.environ.copy()
        env.update(config.env)
        return env
    
    def _command_exists(self, command: str) -> bool:
        """Check if command exists in PATH"""
        try:
            subprocess.run([command, "--version"], 
                         capture_output=True, 
                         timeout=5)
            return True
        except (subprocess.SubprocessError, FileNotFoundError, subprocess.TimeoutExpired):
            return False
    
    def _get_platform_process_args(self) -> Dict[str, Any]:
        """Get platform-specific process arguments"""
        if sys.platform == "win32":
            return {"creationflags": subprocess.CREATE_NEW_PROCESS_GROUP}
        else:
            return {"preexec_fn": os.setsid}
    
    def _start_monitoring(self) -> None:
        """Start process monitoring thread"""
        self._monitor_running = True
        self._monitor_thread = threading.Thread(target=self._monitor_processes, daemon=True)
        self._monitor_thread.start()
    
    def _monitor_processes(self) -> None:
        """Monitor process health in background thread"""
        while self._monitor_running:
            try:
                with self._lock:
                    for name, process_info in self.processes.items():
                        if process_info.process and process_info.status == ProcessStatus.RUNNING:
                            # Check if process is still alive
                            if process_info.process.poll() is not None:
                                # Process has died
                                process_info.status = ProcessStatus.CRASHED
                                self._log("ERROR", f"Server '{name}' crashed unexpectedly")
                                
                                # Read any error output
                                try:
                                    stderr_output = process_info.process.stderr.read()
                                    if stderr_output:
                                        process_info.error_message = stderr_output
                                        self._log("ERROR", f"Server '{name}' error output: {stderr_output}")
                                except:
                                    pass
                
                time.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                self._log("ERROR", f"Monitor thread error: {e}")
                time.sleep(10)  # Wait longer on error
    
    def __del__(self):
        """Destructor to ensure cleanup"""
        try:
            self.cleanup()
        except:
            pass


# Testing and example usage
if __name__ == "__main__":
    def test_log_callback(level: str, message: str):
        print(f"[{level}] {message}")
    
    # Create process manager
    manager = MCPProcessManager(log_callback=test_log_callback)
    
    try:
        # Create test server config
        from mcp_config_parser import MCPServerConfig
        
        test_config = MCPServerConfig(
            name="test_echo",
            command="echo",
            args=["Hello from MCP server!"],
            timeout=30
        )
        
        # Test server lifecycle
        print("Testing server lifecycle...")
        
        # Start server
        if manager.start_server(test_config):
            print("✓ Server started")
            
            # Check status
            status = manager.get_server_status("test_echo")
            if status:
                print(f"✓ Server status: {status.status.value}")
                print(f"✓ Server PID: {status.pid}")
                print(f"✓ Server uptime: {status.uptime:.2f}s")
            
            # Wait a moment
            time.sleep(2)
            
            # Stop server
            if manager.stop_server("test_echo"):
                print("✓ Server stopped")
            else:
                print("✗ Failed to stop server")
        else:
            print("✗ Failed to start server")
    
    finally:
        # Cleanup
        manager.cleanup()
        print("✓ Cleanup completed")
